{"discover": "Keşfet", "all": "Tümü", "yourStories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "noStories": "<PERSON><PERSON> hikayen yok.", "createStory": "Hikaye oluştur", "noFavorites": "<PERSON><PERSON><PERSON>", "addFavoritesHint": "Burada görmek için hikayeleri favorilerine ekle.", "recordVoice": "<PERSON><PERSON><PERSON>", "recordInstructions": "<PERSON><PERSON>ır olduktan sonra 'Kaydet' tuşuna bas ve aşağıdaki metni sessiz bir ortamda, masal okuma hızında ve tonlamasında oku. Bittiğinde kaydı durdur.", "startRecording": "Kaydetmeye Başla", "finishRecording": "Kaydı Bitir (%{0}s)", "setupChildProfile": "Çocuk profili oluşturalım", "childProfileExplanation": "Bu bilgiler sadece çocuğunuz için kişiselleştirilmiş hikayeler oluşturmak için kullanılacak.", "firstName": "İSİM", "childFirstName": "Çocuğunuzun ismi", "age": "YAŞ", "childAge": "Çocuğunuzun yaşı", "gender": "CİNSİYET", "pleaseSelect": "Lütfen Seçin", "male": "<PERSON><PERSON><PERSON>", "female": "Kadın", "continue": "<PERSON><PERSON>", "fillAllFields": "Lütfen tüm alanları doldurun", "pickStoryType": "Hikaye türü seç", "chooseStoryType": "Başlamak için bir hikaye türü seçin.", "pickStoryMoral": "Hikaye öğretisi seç", "storyMoralExplanation": "(İsteğe bağlı) Hikayenize derinlik katmak için bir öğreti seçebilirsiniz.", "pickStoryTheme": "<PERSON>ka<PERSON> konusu seç", "chooseTheme": "Hikayeniz için bir konu seçin. \"Özel\" se<PERSON><PERSON><PERSON><PERSON> se<PERSON>, hikayeyi istediğiniz konuyu yazarak özelleştirebilirsiniz.", "customThemePrompt": "Aklınızdaki konu...", "addOtherCharacters": "<PERSON><PERSON><PERSON> ka<PERSON>leri e<PERSON>", "mainCharacter": "%{0} Hikayeleri", "addCharacterMainCharacter": "<PERSON>:", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otherCharactersExplanation": "(İsteğe bağlı) Hikayenize başka karakterler ekleyebilirsiniz.", "natureSounds": "🌲 <PERSON><PERSON><PERSON>", "natureSoundsDescription": "Çocuğunuzun uyumasına yardımcı olacak rahatlatıcı doğa sesleri", "rain": "Yağ<PERSON><PERSON>", "wind": "<PERSON><PERSON><PERSON><PERSON>", "forest": "<PERSON><PERSON>", "lake": "<PERSON><PERSON><PERSON>", "storyExample": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> bir başak tarlasında yaşayan küçük bir serçe vardı. Her sabah erkenden uyanır, uçup tarlada dans ederdi. <PERSON><PERSON> gü<PERSON>, tarlada altın rengi bir başak gördü. Parlak başağa dokunmak için heyecanla yaklaştı, fakat başak ona seslendi:\n'Merhaba küçük serçe! Eğ<PERSON> bana dokunursan, b<PERSON><PERSON><PERSON> yapacağım!'\nKüçük serçe şaşkın bir şekilde durdu ve sordu: 'Ne büyüsü?'\nBaşak gülümsedi: '<PERSON><PERSON><PERSON> bana nazik davran<PERSON>rsan, sana hayatın güzelliklerini göstereceğim.'\n<PERSON><PERSON><PERSON>, başağa hafifçe dokundu. O anda gökyüzü daha mavi, çiçekler daha parlak oldu. <PERSON><PERSON><PERSON> sevinçle uça<PERSON>, başak ona seslendi:\n'Her zaman nazik ol, <PERSON>ünk<PERSON> dünya da sana nazik olacaktır.'\n<PERSON><PERSON><PERSON>, o günden sonra her canlıya sevgi ve nezaketle yaklaştı. Ve her seferinde dünyası daha da güzel oldu.", "availableVoices": "<PERSON><PERSON><PERSON>", "ready": "Hazır", "generating": "Oluşturuluyor...", "toBeGenerated": "Oluşturulacak", "listen": "<PERSON><PERSON>", "clone": "Klon", "readingTime": "Okuma Süresi: %{minutes} dk", "yourVoiceClones": "<PERSON><PERSON>", "newClone": "<PERSON><PERSON>", "createClone": "Klon Oluştur", "storyReadingGenerating": "Hikaye Okuması Oluşturuluyor", "storyReadyInMinute": "Hikayen bir dakika içinde hazır olacak\nLütfen bekle...", "characterName": "<PERSON><PERSON><PERSON>", "sounds": "<PERSON>sler", "animalSounds": "🐶 <PERSON><PERSON>", "animalSoundsDescription": "Çocuğunuzun uyumasına yardımcı olacak hayvan sesleri", "birds": "<PERSON><PERSON><PERSON>", "frogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sheep": "Koyunlar", "playerTimeFormat": "%{mins}:%{secs}", "customMoralPrompt": "<PERSON><PERSON>...", "addMoreCharacters": "<PERSON><PERSON>", "customMoral": "<PERSON><PERSON>", "backSelections": "Seçimlere Geri <PERSON>", "customTheme": "<PERSON><PERSON>", "min": "%{min} dk", "storyGenerating": "Hikaye Oluşturuluyor", "storyReadingGeneratingClone": "Klon Oluşturuluyor", "moralExample1": "<PERSON><PERSON><PERSON><PERSON>", "moralExample2": "<PERSON><PERSON> o<PERSON>ı<PERSON>", "moralExample3": "Çok çalışmanın önemi", "themeExample1": "Dünyayı ziyaret eden uzaylılar", "themeExample2": "<PERSON><PERSON><PERSON> bir orman", "themeExample3": "<PERSON><PERSON><PERSON> u<PERSON>n bir kale", "generateRandomMoral": "Rastgele Öğreti Oluştur", "generateRandomTheme": "<PERSON><PERSON><PERSON><PERSON>", "boyWithEmoji": "👦 <PERSON><PERSON><PERSON>", "girlWithEmoji": "👧 Kız", "adventure": "Macera", "fantasy": "Fantastik", "sciFi": "<PERSON><PERSON><PERSON>", "detective": "Dedektiflik", "humor": "<PERSON><PERSON><PERSON>", "poetry": "Şiirsel", "history": "<PERSON><PERSON><PERSON>", "bedTime": "<PERSON><PERSON><PERSON>", "love": "❤️Sevgi", "friendship": "🤝Arkadaşlık", "sharing": "🤲Paylaşma", "honesty": "🤍Dürüstlük", "kindness": "💝Kibarlık", "patience": "⌛️Sabır", "curiosity": "🔍Merak", "compassion": "🫂Merhamet", "courage": "💪Cesaret", "responsibility": "✅Sorumluluk", "random": "<PERSON><PERSON><PERSON><PERSON>", "superHero": "<PERSON><PERSON><PERSON>", "animals": "<PERSON><PERSON><PERSON>", "dinosaurs": "<PERSON><PERSON><PERSON>", "dragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cats": "<PERSON><PERSON><PERSON>", "dogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unicorns": "Unicorn", "space": "<PERSON><PERSON><PERSON>", "mermaids": "<PERSON><PERSON>", "magicians": "Sihirbazlar", "doctors": "Doktorlar", "motherWithEmoji": "👩 Anne", "fatherWithEmoji": "👨 Baba", "friendWithEmoji": "👬 Arkadaş", "profile": "Profil", "privacyPolicy": "Gizlilik Sözleşmesi", "termsOfService": "Kullanıcı Sözleşmesi", "rateUs": "Bize Oy Ver", "sendFeedback": "<PERSON><PERSON><PERSON>", "feedbackPlaceholder": "<PERSON><PERSON><PERSON>...", "cancel": "İptal", "submit": "<PERSON><PERSON><PERSON>", "success": "Başarılı", "feedbackSubmitted": "<PERSON>nerin için teşekkür ederiz!", "error": "<PERSON><PERSON>", "feedbackError": "Gönderilemedi. Lütfen tekrar dene.", "taleAIPremium": "Tale AI PRO", "getPremium": "✨ PRO Al ✨", "premiumDescription1": "📚 Limitsiz AI Hikaye Üretme", "premiumDescription2": "🎤 <PERSON><PERSON>", "premiumDescription3": "🎁 50 <PERSON><PERSON>e Kredisi / aylık", "deleteStory": "Hika<PERSON>yi <PERSON>l", "deleteStoryConfirmation": "<PERSON><PERSON>ek istediğinize emin misiniz? Silerseniz geri alamazsınız.", "delete": "Sil", "storyDetails": "Hikaye <PERSON>ı", "storyDetailsSubtitle": "Hikayenin süresini ve dilini seç", "specialVoices": "<PERSON><PERSON>", "createReading": "<PERSON><PERSON><PERSON>", "cloneVoices": "Klonlanmış <PERSON>", "standardVoices": "<PERSON><PERSON>", "createAIStories": "AI ile Hikayeler Üret", "createAIStoriesSubtitle": "Çocuğunuz için kişiselleştirilmiş hikayeler üret!", "onboardingVoices": "<PERSON><PERSON><PERSON>", "onboardingVoicesSubtitle": "Hikayeni okuması için 10+ özel ses arasından seçimini yap!", "onboardingClone1": "<PERSON><PERSON><PERSON>", "onboardingClone1Subtitle": "<PERSON><PERSON><PERSON> ve <PERSON> hikayeleri senin sesinle okusun!", "skipThisStep": "Atla ve Uygulamaya Geç", "cloneMyVoiceNow": "<PERSON><PERSON><PERSON>", "onboardingClone3": "<PERSON><PERSON><PERSON>", "onboardingClone3Subtitle": "Sesini klonluyoruz! Bu birkaç dakika sürebilir...", "generateAIStories": "AI Hikayeler Üret", "onboardingClone4": "Klonlanmış <PERSON>", "onboardingClone4Subtitle": "Klonlanmış sesin kullanıma hazır! Artık kendi sesinle AI Hikayeler üretebilirsin.", "createNewReading": "<PERSON>sler", "tenCredits": "10 Kredi", "oneCredit": "1 Kredi", "myVoices": "<PERSON><PERSON>", "deleteVoice": "<PERSON><PERSON>", "deleteVoiceConfirmation": "<PERSON><PERSON> silmek istediğ<PERSON> emin misin? Bu işlemi geri alamazsın.", "listenFromClone": "<PERSON><PERSON><PERSON>", "readingGenerating1": "Hikayenizi analiz ediliyor...", "readingGenerating2": "Hikayeniz için yapay zeka sesi optimize ediliyor...", "readingGenerating3": "Yapay zeka hikayenizi okuyor...", "readingGenerating4": "<PERSON>uma son haline getiriliyor...", "cloneReadingGenerating1": "Sesiniz klonlanıyor...", "cloneReadingGenerating2": "Hikayeniz için yapay zeka sesi optimize ediliyor...", "cloneReadingGenerating3": "Yapay zeka hikayenizi okuyor...", "cloneReadingGenerating4": "<PERSON>uma son haline getiriliyor...", "generatingStory1": "Hikayenizi yapay zekaya anlatılıyor...", "generatingStory2": "Yapay zeka karakterleri üretiyor...", "generatingStory3": "Yapay zeka olay örgüsünü oluşturuyor...", "generatingStory4": "Yapay zeka karakterleri hikayeye ekliyor...", "generatingStory5": "Son rötu<PERSON>lar yapılıyor...", "generatingStory6": "Hikayenin mükemmel olduğundan emin olu<PERSON>...", "generatingClone1": "<PERSON><PERSON><PERSON>...", "generatingClone2": "Sesiniz analiz ediliyor...", "generatingClone3": "<PERSON><PERSON><PERSON> sentezleniyor...", "generatingClone4": "<PERSON><PERSON> için son a<PERSON><PERSON><PERSON><PERSON> ya<PERSON>ı<PERSON>...", "dailyFreeLimitReached": "Günlük Limite Ulaşıldı", "dailyFreeLimitReachedMessage": "Hikaye oluşturmak için günlük ücretsiz limite ulaştınız. Sınırsız hikaye oluşturmanın kilidini açmak için Tale AI Premium'a yükseltin.", "waitTomorrow": "<PERSON><PERSON><PERSON><PERSON> kadar bekle", "userId": "Kullanıcı Kimliği:", "copied": "Kopyalandı", "profilePremiumTitle": "TaleAI Premium'a hoş geldiniz 🎉", "currentCredits": "Ses Kredileriniz: ", "nextRefresh": "<PERSON><PERSON><PERSON>: ", "getMoreCredits": "50 Kredi Daha <PERSON>ın", "creditsSuccess": "50 kredi daha satın alındı!", "creditsError": "Kredi satın alınamadı. Lütfen tekrar deneyin.", "generalErrorTitle": "Oops!", "generalErrorMessage": "Bir şeyler yanlış gitti. Lütfen daha sonra tekrar deneyin.", "createNewReadingTitle": "<PERSON><PERSON>", "createNewReadingDescription": "Bu, %{credits} ses kredilerini kullanacaktır.", "yourVoiceCredits": "<PERSON>s K<PERSON>: %{credits}", "confirm": "<PERSON>aylayın", "reviewRecording": "Kaydı İncele", "reviewRecordingExplanation": "Kaydınızı dinleyebilir ve memnunsanız ses klonunuzu oluşturabilirsiniz.", "pleaseEnterVoiceName": "Lütfen sesiniz i<PERSON>in bir isim girin.", "tapToPause": "Duraklatmak i<PERSON>", "tapToPlay": "Oynatmak iç<PERSON>", "nameOfTheVoice": "<PERSON><PERSON>", "enterVoiceName": "Ses adını girin...", "createVoiceClone": "Ses Klonu Oluştur", "failedToCreateVoiceClone": "Ses klonu oluşturulamadı.", "oops": "Oops!", "screenDoesNotExist": "<PERSON>u ekran mevcut değil.", "goToHomeScreen": "Ana ekrana git!", "onboardingReset": "Onboarding sıfırlandı", "microphonePermissionDenied": "Mikrofon erişim izni reddedildi", "ok": "<PERSON><PERSON>"}