{"discover": "Discover", "all": "All", "yourStories": "Your Stories", "new": "New", "favorites": "Favorites", "noStories": "You don't have any stories.", "createStory": "Create a story", "noFavorites": "No Favorites", "addFavoritesHint": "Add stories to your favorites to see them here.", "recordVoice": "Record Your Voice", "recordInstructions": "After you're ready, press 'Record' and read the text below in a quiet environment, at storytelling pace and tone. Stop the recording when finished.", "startRecording": "Start Recording", "finishRecording": "Finish Recording (%{0}s)", "setupChildProfile": "Let's setup a child profile", "childProfileExplanation": "These details are only used to generate personalized stories for your child.", "firstName": "FIRST NAME", "childFirstName": "Your child's first name", "age": "AGE", "childAge": "Your child's age", "gender": "GENDER", "pleaseSelect": "Please Select", "male": "Male", "female": "Female", "continue": "Continue", "fillAllFields": "Please fill in all fields", "pickStoryType": "Pick a story type", "chooseStoryType": "Choose a story type to get started.", "pickStoryMoral": "Pick a story moral", "storyMoralExplanation": "(Optional) You can choose a moral to add a layer of depth to your story.", "pickStoryTheme": "Pick a story theme", "chooseTheme": "Choose a theme to create your story. If you choose \"Custom\", you can customize the story with a prompt.", "customThemePrompt": "Enter your custom theme prompt...", "addOtherCharacters": "Add other characters", "mainCharacter": "%{0}'s Stories", "addCharacterMainCharacter": "Main Character:", "change": "Change", "otherCharactersExplanation": "(Optional) You can add other characters to your story.", "natureSounds": "🌲 Nature Sounds", "natureSoundsDescription": "Relaxing nature sounds to help your kid sleep", "rain": "Rain", "wind": "Wind", "forest": "Forest", "lake": "Lake", "storyExample": "Once upon a time, there was a little sparrow living in a green wheat field. Every morning, he would wake up early and dance in the field. One day, he saw a golden wheat spike in the field. Excited to touch the shiny spike, he approached, but the spike spoke:\n'Hello little sparrow! If you touch me, I'll cast a spell!'\nThe little sparrow stopped in surprise and asked: 'What kind of spell?'\nThe spike smiled: 'If you treat me kindly, I'll show you the beauties of life.'\nThe sparrow gently touched the spike. At that moment, the sky became bluer, the flowers brighter. As the sparrow flew joyfully, the spike called out:\n'Always be kind, because the world will be kind to you.'\nFrom that day on, the sparrow approached every living being with love and kindness. And each time, his world became even more beautiful.", "availableVoices": "Available Voices", "ready": "Ready", "generating": "Generating...", "toBeGenerated": "To be generated", "listen": "Listen", "clone": "<PERSON><PERSON>", "readingTime": "Reading Time: %{minutes} min", "yourVoiceClones": "Your Voice Clones", "newClone": "New Clone", "createClone": "<PERSON><PERSON> <PERSON><PERSON>", "storyReadingGenerating": "Story Reading is Getting Generated", "storyReadyInMinute": "Your story will be ready in a minute\nPlease wait...", "characterName": "Character Name", "sounds": "Sounds", "animalSounds": "🐶 Animal Sounds", "animalSoundsDescription": "Animal sounds to help your kid sleep", "birds": "Birds", "frogs": "Frogs", "sheep": "Sheep", "playerTimeFormat": "%{mins}:%{secs}", "customMoralPrompt": "Enter your custom moral prompt...", "addMoreCharacters": "Add More Characters", "customMoral": "Custom Moral", "backSelections": "Back to Selections", "customTheme": "Custom Theme", "min": "%{min} min", "storyGenerating": "Story is Generating", "storyReadingGeneratingClone": "Clone is Getting Generated", "moralExample1": "The importance of kindness", "moralExample2": "The importance of being nice", "moralExample3": "The importance of working hard", "themeExample1": "Aliens visiting earth", "themeExample2": "A magical forest", "themeExample3": "A magical flying castle", "generateRandomMoral": "Generate Random Moral", "generateRandomTheme": "Generate Random Theme", "boyWithEmoji": "👦 Boy", "girlWithEmoji": "👧 Girl", "adventure": "Adventure", "fantasy": "Fantasy", "sciFi": "Science Fiction", "detective": "Detective", "humor": "<PERSON><PERSON>", "poetry": "Poetry", "history": "History", "bedTime": "Bedtime", "love": "❤️Love", "friendship": "🤝Friendship", "sharing": "🤲Sharing", "honesty": "🤍Honesty", "kindness": "💝Kindness", "patience": "⌛️Patience", "curiosity": "🔍Curiosity", "compassion": "🫂Compassion", "courage": "💪Courage", "responsibility": "✅Responsibility", "random": "Random", "superHero": "Super Hero", "animals": "Animals", "dinosaurs": "Dinosaurs", "dragons": "Dragons", "cats": "Cats", "dogs": "Dogs", "unicorns": "Unicorns", "space": "Space", "mermaids": "Mermaids", "magicians": "Magicians", "doctors": "Doctors", "motherWithEmoji": "👩 Mother", "fatherWithEmoji": "👨 Father", "friendWithEmoji": "👬 Friend", "profile": "Profile", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "rateUs": "Rate Us", "sendFeedback": "Send Feedback", "feedbackPlaceholder": "Your feedback...", "cancel": "Cancel", "submit": "Submit", "success": "Success", "feedbackSubmitted": "Thank you for your feedback!", "error": "Error", "feedbackError": "Failed to submit feedback. Please try again later.", "taleAIPremium": "Tale AI Premium", "getPremium": "✨ Get Premium ✨", "premiumDescription1": "📚 Unlimited Story Generation", "premiumDescription2": "🎤 Generate Your Own Voice Clones", "premiumDescription3": "🎁 50 Voice Credits / month", "deleteStory": "Delete Story", "deleteStoryConfirmation": "Are you sure you want to delete this story? You cannot undo this action.", "delete": "Delete", "storyDetails": "Story Details", "storyDetailsSubtitle": "Choose the duration of your story", "specialVoices": "Special Voices", "createReading": "Create Reading", "cloneVoices": "<PERSON><PERSON> Voices", "standardVoices": "Standard Voices", "createAIStories": "Create AI Stories", "createAIStoriesSubtitle": "Create personalized stories for your child!", "onboardingVoices": "Chose Your Favorite Voices", "onboardingVoicesSubtitle": "Pick from 10+ special voices to narrate your stories!", "onboardingClone1": "Clone Your Voice", "onboardingClone1Subtitle": "Just clone your voice and let Tale AI read the stories for you!", "skipThisStep": "Skip and Start Using the App", "cloneMyVoiceNow": "<PERSON>lone My Voice Now", "onboardingClone3": "Cloning Your Voice", "onboardingClone3Subtitle": "We're cloning your voice. This might take a few minutes...", "generateAIStories": "Generate AI Stories", "onboardingClone4": "Your Clone is Ready", "onboardingClone4Subtitle": "Your clone is ready to use! You can now generate AI stories with your own voice.", "createNewReading": "Create New Reading", "tenCredits": "10 Credits", "oneCredit": "1 Credit", "myVoices": "My Voices", "deleteVoice": "Delete Voice", "deleteVoiceConfirmation": "Are you sure you want to delete this voice? You cannot undo this action.", "listenFromClone": "Listen from Your Clone", "readingGenerating1": "Analyzing your story...", "readingGenerating2": "Optimizing the AI Voice for your story...", "readingGenerating3": "AI Voice is reading your story...", "readingGenerating4": "Polishing the reading...", "cloneReadingGenerating1": "Cloning your voice...", "cloneReadingGenerating2": "Optimizing the AI Voice for your story...", "cloneReadingGenerating3": "AI Voice is reading your story...", "cloneReadingGenerating4": "Polishing the reading...", "generatingStory1": "Telling your story to AI...", "generatingStory2": "AI is generating the characters...", "generatingStory3": "AI is generating the plot...", "generatingStory4": "AI is adding the characters to the story...", "generatingStory5": "Making the final touches...", "generatingStory6": "Making sure the story is perfect...", "generatingClone1": "Uploading your voice...", "generatingClone2": "Analyzing your voice...", "generatingClone3": "Re-synthesizing your voice...", "generatingClone4": "Making final adjustments to the clone...", "dailyFreeLimitReached": "Daily Free Limit Reached", "dailyFreeLimitReachedMessage": "You have reached the daily free limit for creating stories. Upgrade to Tale AI Premium to unlock unlimited story generation.", "waitTomorrow": "Wait until tomorrow", "userId": "User ID:", "copied": "Copied to clipboard", "profilePremiumTitle": "Welcome to TaleAI Premium 🎉", "currentCredits": "Your Voice Credits: ", "nextRefresh": "Credit Renewal Date: ", "getMoreCredits": "Get 50 More Credits", "creditsSuccess": "Successfully purchased 50 more credits!", "creditsError": "Failed to purchase credits. Please try again.", "generalErrorTitle": "Oops!", "generalErrorMessage": "Something went wrong. Please try again later.", "createNewReadingTitle": "Create New Reading", "createNewReadingDescription": "This will use %{credits} voice credits.", "yourVoiceCredits": "Your Voice Credits: %{credits}", "confirm": "Confirm", "reviewRecording": "Review Recording", "reviewRecordingExplanation": "You can listen to your recording and if you are happy with it create your voice clone.", "pleaseEnterVoiceName": "Please enter a name for your voice.", "tapToPause": "Tap to Pause", "tapToPlay": "Tap to Play", "nameOfTheVoice": "Name of the Voice", "enterVoiceName": "Enter voice name...", "createVoiceClone": "Create <PERSON>", "failedToCreateVoiceClone": "Failed to create voice clone.", "oops": "Oops!", "screenDoesNotExist": "This screen doesn't exist.", "goToHomeScreen": "Go to home screen!", "onboardingReset": "Onboarding reset", "microphonePermissionDenied": "Permission to access microphone was denied", "ok": "OK", "errorPlayingSound": "Error playing sound preview. Please try again later."}