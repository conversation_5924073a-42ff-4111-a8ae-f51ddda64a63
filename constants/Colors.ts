/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */


export const Colors = {
  light: {
    text: '#FFFFFF',
    background: '#141822',
    tabBarBackground: '#21283F',
    tint: '#6B55F3', // UNCHANGED
    tintPurple: "#6B55F3",
    icon: '#687076', // UNCHANGED
    tabIconDefault: '#9A9DAC',
    tabIconSelected: '#6B55F3',
    likeRed: '#FF3B30',
    stopRed: '#D61F1F',
    brightRed: '#FF3333',
    gray: '#EBEBF599',
    darkBlueGray: '#21283F',
    placeholder: '#666666',
    brightGreen: '#00D46C',
    appStoreLightBlue: '#3EA9F0',
    blueCharcoal: '#1D2436',
    gold: '#FFD700',
    brightOrange: '#FF913C'
  },
};
