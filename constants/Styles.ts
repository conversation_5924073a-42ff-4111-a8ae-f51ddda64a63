import { StyleSheet } from 'react-native';
import { Colors } from './Colors'

export const MAIN_STYLES = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors['light'].background
  },
  mainView: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors['light'].background,
  },
  flex1: {
    flex: 1
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  col: {
    flexDirection: 'column'
  },
  input: {
    flex: 1,
    height: 50,
    backgroundColor: '#FFFFFF0D',
    borderRadius: 16,
    paddingHorizontal: 16,
    color: Colors['light'].text,
    fontSize: 16,
  },
  mainButton: {
    backgroundColor: Colors['light'].tint,
    height: 56,
    padding: 16,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainButtonDisabled: {
    opacity: 0.5
  },
  mainButtonText: {
    color: Colors['light'].text,
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    color: Colors.light.text,
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8
  },
  titleHighlight: {
    color: Colors.light.tint,
  },
  subTitle: {
    color: Colors.light.gray,
    fontSize: 16,
    marginBottom: 16
  },
  selectionButton: {
    backgroundColor: Colors['light'].darkBlueGray,
    height: 56,
    padding: 16,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionButtonSelected: {
    borderColor: Colors['light'].tint,
    borderWidth: 2,
  },
  selectionButtonText: {
    color: Colors['light'].text,
    fontSize: 16,
    fontWeight: '400',
  },
  linePicker: { 
    flexDirection: 'row',
    marginTop: 10,
    backgroundColor: '#FFFFFF0D',
    gap: 4,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  linePickerItem: {
    flex: 1,
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'transparent',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.tint,
  },
  linePickerItemSelected: {
    backgroundColor: Colors.light.tint,
  },
  linePickerItemText: {
    color: Colors.light.text,
    fontSize: 14,
    fontWeight: 'bold',
  },
  borderedCard: {
    backgroundColor: Colors['light'].darkBlueGray,
    borderColor: Colors['light'].tint,
    borderWidth: 1,
    borderRadius: 16,
    padding: 16,
  },
  dividerText: {
    color: Colors.light.gray,
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  grayLine: {
    height: 2,
    backgroundColor: Colors.light.gray,
    flex: 1,
  },
  headerContainer: {
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  centerText: {
    textAlign: 'center',
    width: '100%',
  }
});
