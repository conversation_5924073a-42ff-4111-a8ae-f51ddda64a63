# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TaleAI is a React Native mobile application built with Expo that generates AI-powered stories for children with audio narration capabilities. The app supports multiple languages, child profile management, and includes monetization through in-app purchases.

## Development Commands

### Core Commands
```bash
# Install dependencies
npm install

# Start development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android

# Run tests
npm test

# Lint code
npm run lint

# Build iOS app (requires Xcode)
cd ios && pod install
npx expo run:ios --device  # For physical device
npx expo run:ios           # For simulator

# EAS Build commands
eas build --platform ios
eas build --platform android
```

### Testing Commands
```bash
# Run all tests
npm test

# Run tests in watch mode (default)
jest --watchAll

# Run specific test file
jest path/to/test.test.tsx

# Run tests with coverage
jest --coverage
```

## Architecture

### File-Based Routing Structure
The app uses Expo Router with the following navigation structure:
- `/app/(onboarding)/` - Onboarding flow screens
- `/app/(tabs)/` - Main tab navigation
  - `(discover)/` - Story discovery and browsing
  - `(new_story)/` - Story creation flow
  - `profile.tsx` - User profile management
  - `sounds.tsx` - Audio/sounds management
- `/app/player.tsx` - Audio player screen
- `/app/_layout.tsx` - Root layout with global providers

### State Management
The app uses React Context API with the following key contexts:
- `AudioContext` - Manages audio playback state and controls
- `StoryContext` - Handles story data and generation
- `SpecialVoicesContext` - Manages special voice features and voice cloning

### Utility Modules
Business logic is separated into utility modules in `/components/util/`:
- `CacheUtil.ts` - Image caching and updates
- `LocalizationUtil.ts` - i18n handling with `getTranslation()`
- `StorageUtil.ts` - AsyncStorage wrapper
- `NetworkUtil.ts` - API calls and network handling

### Localization
All UI strings must be added to `/localizations/en.json` (and other language files) and accessed using:
```typescript
import { getTranslation } from '@/components/util/LocalizationUtil';
const text = getTranslation('key.path');
```

### API Integration
- Authentication and monetization: Superwall SDK (migrating from Adapty)
- Analytics: Firebase Analytics
- Crash reporting: Firebase Crashlytics
- Story generation: Custom API endpoints

## Code Conventions

1. **TypeScript**: Strict mode enabled, use proper typing
2. **Imports**: Use absolute imports with `@/` prefix
3. **Components**: Functional components with hooks only
4. **Styling**: Use centralized styles from `/constants/`
5. **State**: Prefer Context API over prop drilling
6. **Async**: Use async/await over promises
7. **Error Handling**: Always handle errors in API calls and async operations

## Key Features to Consider

1. **Child Profiles**: Multiple child profiles per account with age-appropriate content
2. **Story Generation**: AI-powered story creation with themes and morals
3. **Audio Playback**: Streaming audio with background playback support
4. **Voice Recording**: User can record custom narrations
5. **Offline Support**: Image caching for offline viewing
6. **Monetization**: In-app purchases via Superwall (migrating from Adapty)

## iOS-Specific Development

When working with iOS features:
1. Always run `cd ios && pod install` after adding native dependencies
2. Use Xcode workspace file: `ios/TaleAI.xcworkspace`
3. Check Info.plist for permissions and configurations
4. Test on both simulator and physical devices for audio features

## Common Pitfalls

1. **Localization**: Never hardcode strings - always use `getTranslation()`
2. **Audio**: Test audio playback thoroughly on both platforms
3. **Navigation**: Use Expo Router navigation methods, not React Navigation directly
4. **Permissions**: Always check and request permissions before using device features
5. **Cache**: Consider cache invalidation when updating remote content

## Testing Guidelines

1. Add tests for new utility functions
2. Test components with different locales
3. Mock Context providers in tests
4. Test error states and edge cases
5. Verify audio playback behavior on both platforms