import { getLocale } from '@/components/util/LocalizationUtil';
import { API_URL } from '@/constants/Constants';
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useEffect } from 'react';

export interface SpecialVoice {
    id: number;
    name: string;
}

interface SpecialVoicesContextType {
    specialVoices: SpecialVoice[];
    setSpecialVoices: (voices: SpecialVoice[]) => void;
}

const SpecialVoicesContext = createContext<SpecialVoicesContextType | undefined>(undefined);

interface SpecialVoicesProviderProps {
    children: ReactNode;
}

export function SpecialVoicesProvider({ children }: SpecialVoicesProviderProps) {
    const [specialVoices, setSpecialVoices] = useState<SpecialVoice[]>([]);

    const value = {
        specialVoices,
        setSpecialVoices,
    };

    const getSpecialVoices = async () => {
        try {
            const locale = getLocale();
            const response = await fetch(`${API_URL}/get_special_voices/${locale}`);
            const data = await response.json();

            console.log("get_special_voices: ", data);
            if (data.status === 'OK') {
                setSpecialVoices(data.voices);
            }
        } catch (e) {
            console.log("Special voices error: ", e);
        }
    }

    useEffect(() => {
        getSpecialVoices();
    }, []);

    return (
        <SpecialVoicesContext.Provider value={value}>
            {children}
        </SpecialVoicesContext.Provider>
    );
}

export function useSpecialVoices() {
    const context = useContext(SpecialVoicesContext);
    if (context === undefined) {
        throw new Error('useSpecialVoices must be used within a SpecialVoicesProvider');
    }
    return context;
}
