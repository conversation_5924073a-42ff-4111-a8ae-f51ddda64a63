import React, { createContext, useContext, useState } from 'react';
import en from '@/localizations/en.json';
import tr from '@/localizations/tr.json';

// INTERFACES
export interface Character {
  id: string;
  name: string;
  relation: string;
}

export interface ChildProfile {
  name: string;
  age: number;
  gender: number;
}

interface StoryData {
  childProfile: ChildProfile;
  characters: Character[];
  storyType: number | null;
  storyMoral: number | null;
  storyTheme: number | null;
  customThemePrompt: string;
  customMoralPrompt: string;
  storyDuration: number;
}

interface StoryContextType {
  storyData: StoryData;
  updateStoryData: (data: Partial<StoryData>) => void;
}

const defaultStoryData: StoryData = {
  childProfile: {
    name: '',
    age: 0,
    gender: 0,
  },
  characters: [],
  storyType: null,
  storyMoral: null,
  storyTheme: null,
  customThemePrompt: '',
  customMoralPrompt: '',
  storyDuration: 1,
};

// POSSIBLE VALUES
export const STORY_TYPES = [
  { id: 1, translationKey: 'adventure', image: require('../assets/images/storyParameters/adventure.webp') },
  { id: 2, translationKey: 'fantasy', image: require('../assets/images/storyParameters/fantasy.webp') },
  { id: 3, translationKey: 'sciFi', image: require('../assets/images/storyParameters/sciFi.webp') },
  { id: 4, translationKey: 'detective', image: require('../assets/images/storyParameters/detective.webp') },
  { id: 5, translationKey: 'humor', image: require('../assets/images/storyParameters/humor.webp') },
  { id: 6, translationKey: 'poetry', image: require('../assets/images/storyParameters/poetry.webp') },
  { id: 7, translationKey: 'history', image: require('../assets/images/storyParameters/history.webp') },
  { id: 8, translationKey: 'bedTime', image: require('../assets/images/storyParameters/bedTime.webp') },
]

export const STORY_MORALES = [
  { id: 1, translationKey: 'love', image: require('../assets/images/storyParameters/love.webp') },
  { id: 2, translationKey: 'friendship', image: require('../assets/images/storyParameters/friendship.webp') },
  { id: 3, translationKey: 'sharing', image: require('../assets/images/storyParameters/sharing.webp') },
  { id: 4, translationKey: 'honesty', image: require('../assets/images/storyParameters/honesty.webp') },
  { id: 5, translationKey: 'kindness', image: require('../assets/images/storyParameters/kindness.webp') },
  { id: 6, translationKey: 'patience', image: require('../assets/images/storyParameters/patience.webp') },
  { id: 7, translationKey: 'curiosity', image: require('../assets/images/storyParameters/curiosity.webp') },
  { id: 8, translationKey: 'compassion', image: require('../assets/images/storyParameters/compassion.webp') },
  { id: 9, translationKey: 'courage', image: require('../assets/images/storyParameters/courage.webp') },
  { id: 10, translationKey: 'responsibility', image: require('../assets/images/storyParameters/responsibility.webp') },
];

export const STORY_THEMES = [
  { id: 1, translationKey: 'random', image: require('../assets/images/storyParameters/random.webp') },
  { id: 2, translationKey: 'superHero', image: require('../assets/images/storyParameters/superHero.webp') },
  { id: 3, translationKey: 'animals', image: require('../assets/images/storyParameters/animals.webp') },
  { id: 4, translationKey: 'dinosaurs', image: require('../assets/images/storyParameters/dinosaurs.webp') },
  { id: 5, translationKey: 'dragons', image: require('../assets/images/storyParameters/dragons.webp') },
  { id: 6, translationKey: 'cats', image: require('../assets/images/storyParameters/cats.webp') },
  { id: 7, translationKey: 'dogs', image: require('../assets/images/storyParameters/dogs.webp') },
  { id: 8, translationKey: 'unicorns', image: require('../assets/images/storyParameters/unicorns.webp') },
  { id: 9, translationKey: 'space', image: require('../assets/images/storyParameters/space.webp') },
  { id: 10, translationKey: 'mermaids', image: require('../assets/images/storyParameters/mermaids.webp') },
  { id: 11, translationKey: 'magicians', image: require('../assets/images/storyParameters/magicians.webp') },
  { id: 12, translationKey: 'doctors', image: require('../assets/images/storyParameters/doctors.webp') }
];

export const STORY_MORAL_EXAMPLES = ['moralExample1', 'moralExample2', 'moralExample3'];
export const STORY_THEME_EXAMPLES = ['themeExample1', 'themeExample2', 'themeExample3'];


export const getStoryDataAPICompatible = (storyData: StoryData, language: string) => {
  const translations = language === 'tr' ? tr : en;
  const moralText = storyData.customMoralPrompt.length > 0
    ? storyData.customMoralPrompt
    : storyData.storyMoral
      ? translations[STORY_MORALES[storyData.storyMoral - 1].translationKey]
      : '';
  const typeText = storyData.storyType ? translations[STORY_TYPES[storyData.storyType - 1].translationKey] : '';
  const themeText = storyData.customThemePrompt.length > 0
    ? storyData.customThemePrompt
    : storyData.storyTheme
      ? translations[STORY_THEMES[storyData.storyTheme - 1].translationKey]
      : '';

  const storyDataAPICompatible = {
    storyDuration: storyData.storyDuration,
    childProfile: {
      name: storyData.childProfile.name,
      age: storyData.childProfile.age,
      gender: storyData.childProfile.gender, // 0: boy, 1: girl
    },
    characters: storyData.characters.map(character => ({
      name: character.name,
      relation: character.relation,
    })),
    moral: moralText,
    type: typeText,
    theme: themeText,
  };
  return storyDataAPICompatible;
}

const StoryContext = createContext<StoryContextType | undefined>(undefined);

export function StoryProvider({ children }: { children: React.ReactNode }) {
  const [storyData, setStoryData] = useState<StoryData>(defaultStoryData);

  const updateStoryData = (newData: Partial<StoryData>) => {
    setStoryData(prev => ({ ...prev, ...newData }));
  };

  return (
    <StoryContext.Provider value={{ storyData, updateStoryData }}>
      {children}
    </StoryContext.Provider>
  );
}

export function useStoryContext() {
  const context = useContext(StoryContext);
  if (undefined === context) {
    throw new Error('useStoryContext must be used within a StoryProvider');
  }
  return context;
} 