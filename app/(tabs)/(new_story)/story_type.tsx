import { MAIN_STYLES } from "@/constants/Styles";
import { useRouter } from "expo-router";
import { View, Text, KeyboardAvoidingView, TouchableWithoutFeedback, SafeAreaView, Pressable, Platform, Keyboard, StyleSheet, Dimensions } from "react-native";
import { useState } from "react";
import { STORY_TYPES, useStoryContext } from "@/contexts/StoryContext";
import { getTranslation } from "@/components/util/LocalizationUtil";
import StoryParameterCard from "@/components/StoryParameterCard";
import { MINI_PLAYER_HEIGHT } from "@/constants/Constants";
import { FlatGrid } from "react-native-super-grid";
import * as Haptics from 'expo-haptics';
export default function StoryType() {
    const router = useRouter();
    const { updateStoryData } = useStoryContext();
    const parameterWidth = (Dimensions.get('window').width - 48) / 2;
    const [selectedType, setSelectedType] = useState<number | null>(null);

    const progressToStoryMoral = () => {
        updateStoryData({ storyType: selectedType });
        router.push('/story_moral');
    }

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}
        >
            <SafeAreaView style={MAIN_STYLES.safeArea}>
                <View style={[MAIN_STYLES.mainView, { paddingHorizontal: 0 }]}>
                    <Text style={[MAIN_STYLES.title, { marginLeft: 16 }]}>
                        {getTranslation('pickStoryType')}
                    </Text>
                    <Text style={[MAIN_STYLES.subTitle, { marginLeft: 16 }]}>
                        {getTranslation('chooseStoryType')}
                    </Text>

                    <FlatGrid
                        data={STORY_TYPES}
                        itemDimension={parameterWidth}
                        spacing={16}
                        showsVerticalScrollIndicator={false}
                        renderItem={({ item }) =>
                            <Pressable onPress={() => {
                                Haptics.selectionAsync();
                                setSelectedType(item.id)
                            }}>
                                <StoryParameterCard width={parameterWidth} image={item.image} text={getTranslation(item.translationKey)} isSelected={selectedType === item.id} />
                            </Pressable>}
                        style={{ flex: 1 }}
                        contentContainerStyle={{ paddingBottom: 8 }}
                        fixed={true}
                    />

                    <View style={{ paddingHorizontal: 16, paddingTop: 8 }}>
                        <Pressable
                            style={[
                                MAIN_STYLES.mainButton,
                                !selectedType && { opacity: 0.5 }
                            ]}
                            disabled={!selectedType}
                            onPress={progressToStoryMoral}>
                            <Text style={MAIN_STYLES.mainButtonText}>
                                {getTranslation('continue')}
                            </Text>
                        </Pressable>
                    </View>
                </View>
            </SafeAreaView>
        </KeyboardAvoidingView>
    )
}

const styles = StyleSheet.create({
});