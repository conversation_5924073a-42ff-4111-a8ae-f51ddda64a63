import { useRouter } from "expo-router";
import { View, Text, KeyboardAvoidingView, TouchableWithoutFeedback, SafeAreaView, Pressable, Platform, Keyboard, TextInput, ScrollView, StyleSheet } from "react-native";
import { MAIN_STYLES } from "@/constants/Styles";
import { useEffect, useState } from "react";
import { Ionicons } from '@expo/vector-icons';
import { Colors } from "@/constants/Colors";
import { Character, useStoryContext } from "@/contexts/StoryContext";
import { clearChildProfile, getChildProfile } from "@/components/util/ChildProfileUtil";
import { getTranslation } from "@/components/util/LocalizationUtil";
import * as Haptics from 'expo-haptics';

const CharacterInput = ({ character, onUpdate, onDelete }: {
  character: Character;
  onUpdate: (id: string, field: 'name' | 'relation', value: string) => void;
  onDelete: (id: string) => void;
}) => (
  <View style={{
    backgroundColor: Colors.light.darkBlueGray,
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    borderColor: Colors.light.tint,
    borderWidth: 1,
  }}>
    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
      <View style={MAIN_STYLES.flex1} />
      <Pressable onPress={() => onDelete(character.id)}>
        <Ionicons name="close-circle" size={24} color="red" />
      </Pressable>
    </View>

    <TextInput
      value={character.name}
      onChangeText={(text) => onUpdate(character.id, 'name', text)}
      placeholder={getTranslation('characterName')}
      placeholderTextColor={Colors.light.placeholder}
      style={MAIN_STYLES.input}
    />

    <View style={[MAIN_STYLES.linePicker, {marginBottom: 0, backgroundColor: 'transparent'}]}>
      {['motherWithEmoji', 'fatherWithEmoji', 'friendWithEmoji'].map((relation) => (
        <Pressable
          key={relation}
          onPress={() => onUpdate(character.id, 'relation', character.relation === getTranslation(relation) ? '' : getTranslation(relation))}
          style={[MAIN_STYLES.linePickerItem, character.relation === getTranslation(relation) && MAIN_STYLES.linePickerItemSelected]}
        >
          <Text style={MAIN_STYLES.linePickerItemText}>
            {getTranslation(relation)}
          </Text>
        </Pressable>
      ))}
    </View>
  </View>
);

export default function AddCharacter() {
  const router = useRouter();
  const { updateStoryData } = useStoryContext();
  const [characters, setCharacters] = useState<Character[]>([]);
  const [childName, setChildName] = useState('');

  useEffect(() => {
    const getChildName = async () => {
      const childProfile = await getChildProfile();
      if (childProfile) {
        setChildName(childProfile.name);
      }
    }
    getChildName();
  }, []);

  const addCharacter = () => {
    setCharacters([...characters, {
      id: Date.now().toString(),
      name: '',
      relation: ''
    }]);
  };

  const updateCharacter = (id: string, field: 'name' | 'relation', value: string) => {
    if (field === 'relation') {
      Haptics.selectionAsync();
    }
    setCharacters(characters.map(char =>
      char.id === id ? { ...char, [field]: value } : char
    ));
  };

  const deleteCharacter = (id: string) => {
    setCharacters(characters.filter(char => char.id !== id));
  };

  const progressToStoryType = () => {
    updateStoryData({ characters: characters });
    router.push('/story_type');
  }

  return (
    <KeyboardAvoidingView
      style={MAIN_STYLES.flex1}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={MAIN_STYLES.safeArea}>
          <View style={{ flex: 1 }}>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
              <View style={[MAIN_STYLES.mainView, { flex: 1 }]}>
                <Text style={MAIN_STYLES.title}>
                  {getTranslation("addOtherCharacters")} <Text style={MAIN_STYLES.titleHighlight}></Text>
                </Text>
                <Text style={[MAIN_STYLES.subTitle, { marginBottom: 16 }]}>
                  {getTranslation("otherCharactersExplanation")}
                </Text>
                <View style={[MAIN_STYLES.borderedCard, { marginBottom: 16 }]}>
                  <View style={MAIN_STYLES.row}>
                    <View style={MAIN_STYLES.col}>
                      <Text style={[MAIN_STYLES.subTitle, { fontWeight: 'bold', color: Colors.light.text, marginBottom: 4 }]}>
                        {getTranslation("addCharacterMainCharacter")}
                      </Text>
                      <Text style={[MAIN_STYLES.subTitle, { marginBottom: 0, color: Colors.light.text }]}>
                        {childName}
                      </Text>
                    </View>
                    <View style={{ flex: 1 }} />
                    <Pressable onPress={async () => {
                      await clearChildProfile();
                      router.back()
                    }}
                      style={styles.changeButton}>
                      <Text style={[MAIN_STYLES.subTitle, { color: Colors.light.text, marginBottom: 0, fontWeight: 'bold' }]}>
                        {getTranslation("change")}
                      </Text>
                    </Pressable>
                  </View>
                </View>

                {characters.map(character => (
                  <CharacterInput
                    key={character.id}
                    character={character}
                    onUpdate={updateCharacter}
                    onDelete={deleteCharacter}
                  />
                ))}
                <Pressable
                  style={[
                    MAIN_STYLES.mainButton,
                    {
                      borderRadius: 16,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 8,
                      flexDirection: 'row',
                    }
                  ]}
                  onPress={addCharacter}>
                  <Ionicons name="add" size={24} color={Colors['light'].text} />
                  <Text style={MAIN_STYLES.mainButtonText}>
                    {getTranslation("addMoreCharacters")}
                  </Text>
                </Pressable>

                <View style={{ flex: 1 }} />

                <Pressable
                  style={MAIN_STYLES.mainButton}
                  onPress={progressToStoryType}>
                  <Text style={MAIN_STYLES.mainButtonText}>
                    {getTranslation("continue")}
                  </Text>
                </Pressable>
              </View>
            </ScrollView>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  changeButton: {
    marginBottom: 0,
    backgroundColor: Colors.light.tint,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  }
});