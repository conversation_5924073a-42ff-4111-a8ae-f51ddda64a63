import { Stack } from 'expo-router';
import { StoryProvider } from '@/contexts/StoryContext';
import { getTranslation } from '@/components/util/LocalizationUtil';
import { Colors } from '@/constants/Colors';
import { ScreenOptions_NoHeader, ScreenOptions_MinimalBackButtonTransparent } from '@/constants/ScreenOptions';

export default function DiscoverLayout() {
  return (
    <StoryProvider>
      <Stack
        screenOptions={{
          headerBackTitle: '',
          headerTintColor: Colors.light.text,
          headerTransparent: true,
          headerTitle: ''
        }}>
        <Stack.Screen
          name="index"
          options={{
            ...ScreenOptions_NoHeader,
            title: getTranslation('setupChildProfile')
          }}
        />
        <Stack.Screen
          name="add_character"
          options={{
            title: getTranslation('addOtherCharacters'),
            ...ScreenOptions_NoHeader,
          }}
        />
        <Stack.Screen
          name='story_type'
          options={{
            ...ScreenOptions_MinimalBackButtonTransparent,
            title: getTranslation('pickStoryType'),
          }}
        />
        <Stack.Screen
          name='story_moral'
          options={{
            ...ScreenOptions_MinimalBackButtonTransparent,
            title: getTranslation('pickStoryMoral'),
          }}
        />
        <Stack.Screen
          name='story_theme'
          options={{
            ...ScreenOptions_MinimalBackButtonTransparent,
            title: getTranslation('pickStoryTheme'),
          }}
        />
        <Stack.Screen
          name='story_details'
          options={{
            ...ScreenOptions_NoHeader,
            title: getTranslation('storyDetails'),
          }}
        />
      </Stack>
    </StoryProvider>
  );
}