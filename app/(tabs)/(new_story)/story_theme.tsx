import { MAIN_STYLES } from "@/constants/Styles";
import { useRouter } from "expo-router";
import { View, Text, Keyboard, Pressable, TouchableWithoutFeedback, SafeAreaView, TextInput } from "react-native";
import { useState } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { Colors } from "@/constants/Colors";
import { STORY_THEMES, useStoryContext, STORY_THEME_EXAMPLES } from "@/contexts/StoryContext";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { FlatGrid } from "react-native-super-grid";
import { Dimensions } from "react-native";
import StoryParameterCard from '@/components/StoryParameterCard';
import * as Haptics from 'expo-haptics';
import { ThemedText } from "@/components/ThemedText";

export default function StoryTheme() {
    const router = useRouter();
    const { updateStoryData } = useStoryContext();
    const [selectedTheme, setSelectedTheme] = useState<number | null>(null);
    const [customPrompt, setCustomPrompt] = useState('');

    const [isCustomTheme, setIsCustomTheme] = useState(false);
    const [customThemeExampleIndex, setCustomThemeExampleIndex] = useState(0);
    const parameterWidth = (Dimensions.get('window').width - 48) / 2;

    const renderThemes = () => {
        if (isCustomTheme) {
            return (
                <View>
                    <View style={MAIN_STYLES.row}>
                        <Pressable
                            style={[
                                {
                                    borderRadius: 8,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginBottom: 8,
                                    backgroundColor: Colors['light'].stopRed,
                                    flexDirection: 'row',
                                    paddingVertical: 8,
                                    paddingHorizontal: 16,
                                }
                            ]}
                            onPress={() => {
                                setIsCustomTheme(false);
                                setCustomPrompt('');
                            }}>
                            <Ionicons name="arrow-back" size={24} color={Colors['light'].text} />
                            <Text style={MAIN_STYLES.mainButtonText}>{getTranslation('backSelections')}</Text>
                        </Pressable>
                        <View style={MAIN_STYLES.flex1} />
                    </View>
                    <TextInput
                        style={[
                            MAIN_STYLES.input,
                            {
                                textAlignVertical: 'top',
                                padding: 12,
                                minHeight: 160,
                                maxHeight: 160,
                            }
                        ]}
                        multiline
                        placeholder={getTranslation('customThemePrompt')}
                        placeholderTextColor={Colors.light.placeholder}
                        value={customPrompt}
                        onChangeText={setCustomPrompt}
                    />
                    <Pressable
                        style={[{ marginTop: 8, flexDirection: 'row', alignItems: 'center', padding: 8, width: "100%", justifyContent: 'center' }]}
                        onPress={() => {
                            console.log("Hey");
                            const nextIndex = (customThemeExampleIndex + 1) % STORY_THEME_EXAMPLES.length;
                            setCustomThemeExampleIndex(nextIndex);
                            setCustomPrompt(getTranslation(STORY_THEME_EXAMPLES[nextIndex]));
                        }}
                        hitSlop={5}
                        accessible={true}
                    >
                        <Ionicons name="dice" size={24} color={Colors['light'].text} style={{ marginRight: 8 }} />
                        <Text style={[MAIN_STYLES.selectionButtonText, { fontWeight: '700' }]}>{getTranslation('generateRandomTheme')}</Text>
                    </Pressable>
                </View>
            );
        }

        return (
            <>
                <View style={{ marginHorizontal: -16, flex: 1 }}>
                    <FlatGrid
                        data={STORY_THEMES}
                        itemDimension={parameterWidth}
                        spacing={16}
                        showsVerticalScrollIndicator={false}
                        renderItem={({ item }) => (
                            <Pressable
                                onPress={() => {
                                    Haptics.selectionAsync();
                                    setSelectedTheme(item.id)
                                }}>
                                <StoryParameterCard
                                    width={parameterWidth}
                                    image={item.image}
                                    text={getTranslation(item.translationKey)}
                                    isSelected={selectedTheme === item.id}
                                />
                            </Pressable>
                        )}
                        style={{ flex: 1 }}
                        contentContainerStyle={{ paddingBottom: 8 }}
                        fixed={true}
                    />
                </View>
                <View style={[MAIN_STYLES.row, { marginBottom: 8 }]}>
                    <View style={MAIN_STYLES.grayLine} />
                    <Text style={MAIN_STYLES.dividerText}>OR</Text>
                    <View style={MAIN_STYLES.grayLine} />
                </View>
                <Pressable
                    style={[MAIN_STYLES.selectionButton]}
                    onPress={() => {
                        setIsCustomTheme(true);
                        setSelectedTheme(null);
                    }}>
                    <Text style={MAIN_STYLES.selectionButtonText}>{getTranslation('customTheme')}</Text>
                </Pressable>
            </>
        );
    };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <SafeAreaView style={MAIN_STYLES.safeArea}>
                <View style={MAIN_STYLES.mainView}>
                    <ThemedText type='h1'>
                        {getTranslation('pickStoryTheme')}
                    </ThemedText>
                    <Text style={MAIN_STYLES.subTitle}>
                        {getTranslation('chooseTheme')}
                    </Text>
                    <View style={[MAIN_STYLES.flex1]}>
                        {renderThemes()}
                    </View>
                    <Pressable
                        style={[MAIN_STYLES.mainButton,
                        ((isCustomTheme ? customPrompt.length === 0 : selectedTheme === null)) && MAIN_STYLES.mainButtonDisabled,
                        { marginTop: 8 }
                        ]}
                        onPress={() => {
                            updateStoryData({ storyTheme: selectedTheme, customThemePrompt: customPrompt });
                            router.push('/story_details');
                        }}
                        disabled={(isCustomTheme ? customPrompt.length === 0 : selectedTheme === null)}
                    >
                        <Text style={MAIN_STYLES.mainButtonText}>
                            {getTranslation('continue')}
                        </Text>
                    </Pressable>
                </View>
            </SafeAreaView>
        </TouchableWithoutFeedback>
    )
}