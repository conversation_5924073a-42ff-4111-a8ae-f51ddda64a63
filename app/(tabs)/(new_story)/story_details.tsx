import { View, Text, SafeAreaView, Al<PERSON>, Pressable, StyleSheet } from "react-native";

import { saveStory } from "@/components/util/StoryUtil";
import { API_URL } from "@/constants/Constants";
import { getUserIdCreateIfNot } from "@/components/util/LoginUtil";
import { getTranslationWithParams } from "@/components/util/LocalizationUtil";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { useCallback, useEffect, useMemo, useState } from "react";
import { MAIN_STYLES } from "@/constants/Styles";
import { AISphere } from "@/components/AISphere";
import { getLocale } from "@/components/util/LocalizationUtil";
import { useStoryContext } from "@/contexts/StoryContext";
import { Link, useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import Feather from '@expo/vector-icons/Feather';
import { getStoryDataAPICompatible } from "@/contexts/StoryContext";
import TypingText from "@/components/TypingText";
import { displayPaywall, getSubscriptionStatus } from "@/components/util/AdaptyUtil";

const StoryGenerating = ({ percentComplete }: { percentComplete: number }) => {
    const progressMessage = useMemo(() => {
        const generatingTexts = Array.from({ length: 6 }, (_, i) =>
            getTranslation(`${'generatingStory'}${i + 1}`)
        );

        const segmentSize = 100 / generatingTexts.length;
        const index = Math.min(
            Math.floor(percentComplete / segmentSize),
            generatingTexts.length - 1
        );

        return generatingTexts[index];
    }, [percentComplete]);

    return (
        <SafeAreaView style={[MAIN_STYLES.safeArea]}>
            <View style={MAIN_STYLES.headerContainer}>
                <Link dismissTo href="/(new_story)">
                    <Feather name="chevron-left" size={32} color={Colors.light.text} />
                </Link>
            </View>
            <View style={[MAIN_STYLES.mainView, { alignItems: 'center', justifyContent: 'center' }]}>
                <AISphere />
                <Text style={[MAIN_STYLES.title, { textAlign: 'center' }]}>{getTranslation('storyGenerating')}</Text>
                <Text style={styles.percentText}>{Math.round(percentComplete)}%</Text>
                <TypingText text={progressMessage} />
            </View>
        </SafeAreaView>
    );
};

export default function StoryDetails() {
    const router = useRouter();
    const locale = getLocale();
    const [isLoading, setIsLoading] = useState(false);
    const [language, setLanguage] = useState(locale === 'tr' ? 'tr' : 'en');
    const [storyDuration, setStoryDuration] = useState(1);
    const { storyData, updateStoryData } = useStoryContext();
    const [percentComplete, setPercentComplete] = useState(0);

    useEffect(() => {
        if (isLoading && percentComplete < 95) {
            const interval = setInterval(() => {
                const randomIncrement = Math.random() * 2 + 0.5; // Random increment between 0.5 and 2.5
                setPercentComplete(prev => Math.min(prev + randomIncrement, 95));
            }, 1000);

            return () => clearInterval(interval);
        }
    }, [percentComplete, isLoading]);

    const handleSubmit = async () => {
        setIsLoading(true);
        try {
            const currentStory = { ...storyData, storyDuration: storyDuration };
            console.log('handleSubmit', currentStory);

            const storyDataAPICompatible = getStoryDataAPICompatible(currentStory, language);
            console.log('storyDataAPICompatible', storyDataAPICompatible);

            const userIdentifier = await getUserIdCreateIfNot();
            if (userIdentifier === null) {
                throw new Error('User identifier not found');
            }

            const isPro = await getSubscriptionStatus();

            const response = await fetch(`${API_URL}/create_story`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    story_data: storyDataAPICompatible,
                    user_identifier: userIdentifier,
                    language: language,
                    is_pro: isPro,
                }),
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            // Handle the response here when needed
            const result = await response.json();
            console.log('result', result);
            if (result.status === 'OK') {
                console.log('Story created successfully');

                await saveStory(
                    result.story,
                    result.story_id,
                );

                setTimeout(() => {
                    setIsLoading(false);
                    setPercentComplete(95);
                    router.dismiss(5);
                    router.push(`/(tabs)/(discover)/read_story?id=${result.story_id}`);
                }, 5000) // Timeout to let the image get generated
            } else if (result.status === "ERROR_DAILY_FREE_LIMIT_REACHED") {
                Alert.alert(
                    getTranslation('dailyFreeLimitReached'),
                    getTranslation('dailyFreeLimitReachedMessage'),
                    [
                        {
                            text: getTranslation('waitTomorrow'),
                            style: 'cancel',
                        },
                        {
                            text: getTranslation('getPremium'),
                            style: 'default',
                            onPress: async () => {
                                await displayPaywall('general');
                            }
                        },
                    ]
                );
                setIsLoading(false);
                setPercentComplete(0);
            } else {
                throw new Error('Failed to create story');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to create story');
            console.error('Error creating story:', error);
            setIsLoading(false);
            setPercentComplete(0);
        }
    };

    return (
        isLoading ? <StoryGenerating percentComplete={percentComplete} /> :
            <SafeAreaView style={MAIN_STYLES.safeArea}>
                <View style={MAIN_STYLES.headerContainer}>
                    <Link dismissTo href="/(new_story)/story_theme">
                        <Feather name="chevron-left" size={32} color={Colors.light.text} />
                    </Link>
                </View>
                <View style={MAIN_STYLES.mainView}>
                    <ThemedText type='h1'>
                        {getTranslation('storyDetails')}
                    </ThemedText>
                    <Text style={MAIN_STYLES.subTitle}>
                        {getTranslation('storyDetailsSubtitle')}
                    </Text>
                    <View style={MAIN_STYLES.linePicker}>
                        {[1, 2, 3].map((item) => (
                            <Pressable
                                key={item}
                                style={[MAIN_STYLES.linePickerItem, storyDuration === item && MAIN_STYLES.linePickerItemSelected]}
                                onPress={() => setStoryDuration(item)}
                            >
                                <Text style={[MAIN_STYLES.linePickerItemText]}>{getTranslationWithParams('min', { min: item })}</Text>
                            </Pressable>
                        ))}
                    </View>
                    {locale === 'tr' && (
                        <View style={MAIN_STYLES.linePicker}>
                            <Pressable style={[MAIN_STYLES.linePickerItem, language === 'tr' && MAIN_STYLES.linePickerItemSelected]} onPress={() => setLanguage('tr')}>
                                <Text style={[MAIN_STYLES.linePickerItemText]}>Türkçe</Text>
                            </Pressable>
                            <Pressable style={[MAIN_STYLES.linePickerItem, language === 'en' && MAIN_STYLES.linePickerItemSelected]} onPress={() => setLanguage('en')}>
                                <Text style={[MAIN_STYLES.linePickerItemText]}>English</Text>
                            </Pressable>
                        </View>
                    )}
                    <View style={MAIN_STYLES.flex1} />

                    <Pressable
                        style={[MAIN_STYLES.mainButton,
                        isLoading && MAIN_STYLES.mainButtonDisabled,
                        ]}
                        onPress={handleSubmit}
                        disabled={isLoading}
                    >
                        <Text style={MAIN_STYLES.mainButtonText}>
                            {getTranslation('continue')}
                        </Text>
                    </Pressable>
                </View>
            </SafeAreaView>
    )
}

const styles = StyleSheet.create({
    percentText: {
        fontSize: 32,
        fontWeight: '400',
        color: Colors['light'].text,
        textAlign: 'center',
        marginBottom: 32
    },
})