import { useState } from 'react';
import { MAIN_STYLES } from "@/constants/Styles";
import { useRouter } from "expo-router";
import { View, Text, TouchableWithoutFeedback, KeyboardAvoidingView, SafeAreaView, Pressable, Keyboard, Platform, TextInput, StyleSheet } from "react-native";
import { STORY_MORAL_EXAMPLES, STORY_MORALES, useStoryContext } from '@/contexts/StoryContext';
import { getTranslation } from '@/components/util/LocalizationUtil';
import { Colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import { FlatGrid } from "react-native-super-grid";
import { Dimensions } from "react-native";
import StoryParameterCard from '@/components/StoryParameterCard';
import * as Haptics from 'expo-haptics';
import { ThemedText } from '@/components/ThemedText';
export default function StoryMoral() {
    const router = useRouter();
    const { updateStoryData } = useStoryContext();
    const [selectedMoral, setSelectedMoral] = useState<number | null>(null);
    const [isCustomMoral, setIsCustomMoral] = useState(false);
    const [customMoral, setCustomMoral] = useState('');
    const [customMoralExampleIndex, setCustomMoralExampleIndex] = useState(0);
    const parameterWidth = (Dimensions.get('window').width - 48) / 2;

    const progressToStoryTheme = () => {
        updateStoryData({ storyMoral: selectedMoral, customMoralPrompt: customMoral });
        router.push('/story_theme');
    }

    const renderMoralGrid = () => {
        return (
            <>
                <FlatGrid
                    data={STORY_MORALES}
                    itemDimension={parameterWidth}
                    spacing={16}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item }) => (
                        <Pressable
                            onPress={() => {
                                Haptics.selectionAsync();
                                setSelectedMoral(item.id)
                            }}>
                            <View style={
                                [styles.moralCard,
                                { width: parameterWidth },
                                item.id === selectedMoral && { borderColor: Colors['light'].tint, borderWidth: 2 }]}>
                                <ThemedText type='semiBold'>
                                    {getTranslation(item.translationKey)}
                                </ThemedText>
                            </View>
                        </Pressable>
                    )}
                    style={{ flex: 1 }}
                    contentContainerStyle={{ paddingBottom: 8 }}
                    fixed={true}
                />
                <View style={[MAIN_STYLES.row, { marginBottom: 8, paddingHorizontal: 16 }]}>
                    <View style={MAIN_STYLES.grayLine} />
                    <Text style={MAIN_STYLES.dividerText}>OR</Text>
                    <View style={MAIN_STYLES.grayLine} />
                </View>
                <View style={{ paddingHorizontal: 16 }}>
                    <Pressable
                        style={[MAIN_STYLES.selectionButton, { marginBottom: 8 }]}
                        onPress={() => {
                            setIsCustomMoral(true);
                            setSelectedMoral(null);
                        }}>
                        <Text style={MAIN_STYLES.selectionButtonText}>
                            {getTranslation('customMoral')}
                        </Text>
                    </Pressable>
                </View>
            </>
        );
    };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flexGrow: 1 }}
            >
                <SafeAreaView style={MAIN_STYLES.safeArea}>
                    <View style={[MAIN_STYLES.mainView, { paddingHorizontal: 0 }]}>
                        <View style={{ paddingHorizontal: 16 }}>
                            <Text style={MAIN_STYLES.title}>
                                {getTranslation('pickStoryMoral')}
                            </Text>
                            <Text style={MAIN_STYLES.subTitle}>
                                {getTranslation('storyMoralExplanation')}
                            </Text>
                        </View>

                        <View style={{ flex: 1 }}>
                            {isCustomMoral ? (
                                <View style={{ paddingHorizontal: 16 }}>
                                    <View style={MAIN_STYLES.row}>
                                        <Pressable
                                            style={[
                                                {
                                                    borderRadius: 8,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    marginBottom: 8,
                                                    backgroundColor: Colors['light'].stopRed,
                                                    flexDirection: 'row',
                                                    paddingVertical: 8,
                                                    paddingHorizontal: 16,
                                                }
                                            ]}
                                            onPress={() => {
                                                setIsCustomMoral(false);
                                                setCustomMoral('');
                                            }}>
                                            <Ionicons name="arrow-back" size={24} color={Colors['light'].text} />
                                            <Text style={MAIN_STYLES.mainButtonText}>{getTranslation('backSelections')}</Text>
                                        </Pressable>
                                        <View style={MAIN_STYLES.flex1} />
                                    </View>
                                    <TextInput
                                        style={[
                                            MAIN_STYLES.input,
                                            {
                                                textAlignVertical: 'top',
                                                padding: 12,
                                                minHeight: 160,
                                                maxHeight: 160,
                                            }
                                        ]}
                                        multiline
                                        placeholder={getTranslation('customMoralPrompt')}
                                        placeholderTextColor={Colors.light.placeholder}
                                        value={customMoral}
                                        onChangeText={setCustomMoral}
                                        autoCapitalize="none"
                                        keyboardType="default"
                                        returnKeyType="default"
                                        enablesReturnKeyAutomatically
                                        blurOnSubmit={false}
                                    />
                                    <Pressable
                                        style={{ marginTop: 8, flexDirection: 'row', alignItems: 'center', padding: 8, width: "100%", justifyContent: 'center' }}
                                        onPress={() => {
                                            console.log("Hey");
                                            const nextIndex = (customMoralExampleIndex + 1) % STORY_MORAL_EXAMPLES.length;
                                            setCustomMoralExampleIndex(nextIndex);
                                            setCustomMoral(getTranslation(STORY_MORAL_EXAMPLES[nextIndex]));
                                        }}
                                        hitSlop={5}
                                        accessible={true}
                                    >
                                        <Ionicons name="dice" size={24} color={Colors['light'].text} style={{ marginRight: 8 }} />
                                        <Text style={[MAIN_STYLES.selectionButtonText, { fontWeight: '700' }]}>{getTranslation('generateRandomMoral')}</Text>
                                    </Pressable>
                                </View>
                            ) : (
                                renderMoralGrid()
                            )}
                        </View>

                        <View style={{ paddingHorizontal: 16 }}>
                            <Pressable
                                style={[MAIN_STYLES.mainButton]}
                                onPress={progressToStoryTheme}>
                                <Text style={MAIN_STYLES.mainButtonText}>
                                    {getTranslation('continue')}
                                </Text>
                            </Pressable>
                        </View>
                    </View>
                </SafeAreaView>
            </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
    );
}

const styles = StyleSheet.create({
    moralCard: {
        backgroundColor: Colors['light'].blueCharcoal,
        borderRadius: 16,
        padding: 8,
        paddingVertical: 16,
        alignItems: 'center',
        justifyContent: 'center'
    }
})