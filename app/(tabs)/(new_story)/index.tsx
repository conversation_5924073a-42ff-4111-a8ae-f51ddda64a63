import { StyleSheet, View, SafeAreaView, Pressable, Keyboard, TouchableWithoutFeedback, KeyboardAvoidingView, Platform, Text, Modal, Alert, ActivityIndicator } from 'react-native';
import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';

import { MAIN_STYLES } from "@/constants/Styles";
import { useState } from "react";
import { Colors } from "@/constants/Colors";
import { useRouter } from "expo-router";
import TextInputwLabel from '@/components/TextInputwLabel';
import { useStoryContext } from '@/contexts/StoryContext';
import { getChildProfile, saveChildProfile } from '@/components/util/ChildProfileUtil';
import { getTranslation, getTranslationWithParams } from '@/components/util/LocalizationUtil';
import * as Haptics from 'expo-haptics';


export default function HomeScreen() {
  const router = useRouter();
  const { updateStoryData } = useStoryContext();

  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState<number | null>(null);

  const [showGenderPicker, setShowGenderPicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useFocusEffect(
    useCallback(() => {
      const loadChildProfile = async () => {
        try {
          const childProfile = await getChildProfile();
          if (childProfile) {
            setName(childProfile.name);
            setAge(childProfile.age.toString());
            setGender(childProfile.gender);
            updateStoryData({ childProfile: childProfile });
            router.push('/add_character');
          }
        } catch (error) {
          console.error('Error loading child profile:', error);
        }
      };

      loadChildProfile();
    }, [])
  );

  const progressToAddCharacter = async () => {
    setIsLoading(true);
    try {
      if (name.length === 0 || age.length === 0 || gender === null) {
        Alert.alert(getTranslation('fillAllFields'));
        return;
      }
      await saveChildProfile({
        name: name,
        age: parseInt(age),
        gender: gender,
      });
      updateStoryData({
        childProfile: {
          name: name,
          age: parseInt(age),
          gender: gender,
        }
      });
      router.push('/add_character');
    } catch (error) {
      console.error('Error saving child profile:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const onGenderPress = (gender: number) => {
    Haptics.selectionAsync()
    setGender(gender);
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={MAIN_STYLES.safeArea}>
          <View style={MAIN_STYLES.mainView}>
            <Text style={MAIN_STYLES.title}>
              {getTranslation('setupChildProfile')}
            </Text>
            <Text style={MAIN_STYLES.subTitle}>
              {getTranslation('childProfileExplanation')}
            </Text>
            <TextInputwLabel
              label={getTranslation('firstName')}
              value={name}
              onChangeText={setName}
              placeholder={getTranslation('childFirstName')}
            />
            <TextInputwLabel
              label={getTranslation('age')}
              value={age}
              onChangeText={setAge}
              placeholder={getTranslation('childAge')}
              numeric={true}
            />

            <Text style={styles.label}>{getTranslation('gender')}</Text>
            <View style={MAIN_STYLES.linePicker}>
              <Pressable
                style={[MAIN_STYLES.linePickerItem, gender === 0 && MAIN_STYLES.linePickerItemSelected]}
                onPress={() => onGenderPress(0)}>
                <Text style={MAIN_STYLES.linePickerItemText}>{getTranslation('boyWithEmoji')}
                </Text>
              </Pressable>
              <Pressable style={[MAIN_STYLES.linePickerItem, gender === 1 && MAIN_STYLES.linePickerItemSelected]}
                onPress={() => onGenderPress(1)}>
                <Text style={MAIN_STYLES.linePickerItemText}>{getTranslation('girlWithEmoji')}
                </Text>
              </Pressable>
            </View>

            <View style={MAIN_STYLES.flex1} />
            <Pressable
              style={[MAIN_STYLES.mainButton, (isLoading || name.length === 0 || age.length === 0 || gender === null) && MAIN_STYLES.mainButtonDisabled]}
              onPress={progressToAddCharacter}
              disabled={isLoading || name.length === 0 || age.length === 0 || gender === null}>
              {isLoading ? <ActivityIndicator size="small" color={Colors.light.background} /> :
                <Text style={MAIN_STYLES.mainButtonText}>{getTranslation('continue')}</Text>
              }
            </Pressable>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  label: {
    color: Colors.light.gray,
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
});
