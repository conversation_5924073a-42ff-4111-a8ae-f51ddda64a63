    // Start of Selection
    import { router, Tabs } from 'expo-router';
    import React, { useEffect } from 'react';
    import { View, StyleSheet } from 'react-native';
    
    import { TabBarIcon } from '@/components/navigation/TabBarIcon';
    import { Colors } from '@/constants/Colors';
    import { useColorScheme } from '@/hooks/useColorScheme';
    import MiniPlayer from '@/components/MiniPlayer';
    import { useAudio } from '@/contexts/AudioContext';
    import { getTranslation } from '@/components/util/LocalizationUtil';
    
    export default function TabLayout() {
      const colorScheme = useColorScheme();
      const { currentSound } = useAudio();
    
      return (
        <View style={styles.container}>
          <Tabs
            initialRouteName="(discover)"
            screenOptions={{
              tabBarActiveTintColor: Colors['light'].tabIconSelected, // OVERRIDE
              headerShown: false,
              tabBarStyle: {
                backgroundColor: Colors['light'].tabBarBackground,
                borderTopColor: Colors['light'].tabBarBackground,
              },
              tabBarLabelStyle: {
                fontSize: 12,
                fontWeight: '400'
              }
            }}
          >
            <Tabs.Screen
              name="(discover)"
              options={{
                title: getTranslation('discover'),
                tabBarIcon: ({ color, focused }) => (
                  <TabBarIcon name={focused ? 'compass' : 'compass-outline'} color={color} />
                ),
              }}
            />
            <Tabs.Screen
              name="(new_story)"
              options={{
                title: getTranslation('new'),
                tabBarIcon: ({ color, focused }) => (
                  <TabBarIcon name={focused ? 'book' : 'book-outline'} color={color} />
                ),
              }}
            />
            <Tabs.Screen
              name="sounds"
              options={{
                title: getTranslation('sounds'),
                tabBarIcon: ({ color, focused }) => (
                  <TabBarIcon name={focused ? 'musical-notes' : 'musical-notes-outline'} color={color} />
                ),
              }}
            />
            <Tabs.Screen
              name="profile"
              options={{
                title: getTranslation('profile'),
                tabBarIcon: ({ color, focused }) => (
                  <TabBarIcon name={focused ? 'person' : 'person-outline'} color={color} />
                ),
              }}
            />
          </Tabs>
          {currentSound && <MiniPlayer style={styles.miniPlayer} />}
        </View>
      );
    }
    
    const styles = StyleSheet.create({
      container: {
        flex: 1,
        position: 'relative',
      },
      miniPlayer: {
        position: 'absolute',
        bottom: 80, // Adjust this value based on the tab bar height
        left: 0,
        right: 0,
        zIndex: 10, // Ensure it appears above the tab bar
      },
    });
