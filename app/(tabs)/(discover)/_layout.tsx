import { Stack } from 'expo-router';
import { Colors } from '@/constants/Colors';
import { getTranslation } from '@/components/util/LocalizationUtil';

export default function DiscoverLayout() {
  return (
    <Stack screenOptions={{
      headerBackTitle: '',
      headerTintColor: Colors.light.text,
      headerBackButtonDisplayMode: 'minimal',
      headerTransparent: true,
    }}>
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
          title: getTranslation('discover')
        }}
      />
      <Stack.Screen
        name="read_story"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="reading_generating"
        options={{
          headerShown: true,
          headerTransparent: true,
          title: '',
        }}
      />
      <Stack.Screen
        name="new_clone"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="review_recording" options={{
          title: '',
        }} />
    </Stack>
  );
}