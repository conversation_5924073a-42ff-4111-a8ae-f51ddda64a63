import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { View, Text, SafeAreaView, StyleSheet, Pressable, TextInput, ActivityIndicator, Alert, Platform, KeyboardAvoidingView, TouchableWithoutFeedback, Keyboard } from "react-native";
import { useState, useEffect } from "react";
import Ionicons from '@expo/vector-icons/Ionicons';
import { useAudioPlayer, useAudioPlayerStatus } from 'expo-audio';
import * as FileSystem from 'expo-file-system';
import { API_URL } from '@/constants/Constants';
import { saveRecordings } from '@/components/util/RecordingUtil'
import { getUserId, getUserIdCreateIfNot } from '@/components/util/LoginUtil'
import { useLocalSearchParams, useRouter } from 'expo-router';
import { getLocale, getTranslation } from '@/components/util/LocalizationUtil';

export default function ReviewRecordingScreen() {
    const router = useRouter();
    const player = useAudioPlayer();
    const playerStatus = useAudioPlayerStatus(player);
    const { recordingUri, storyId } = useLocalSearchParams();
    const [voiceName, setVoiceName] = useState('');
    const [isUploading, setIsUploading] = useState(false);
    const [isPlaying, setIsPlaying] = useState(false);

    useEffect(() => {
        if (recordingUri && typeof recordingUri === 'string') {
            player.replace({
                uri: recordingUri
            })
        }
    }, [recordingUri])

    useEffect(() => {
        if (playerStatus?.didJustFinish) {
            setIsPlaying(false);
        }
    }, [playerStatus])

    const playSound = async () => {
        try {
            if (player.playing) {
                console.log('Stopping playback');
                player.pause();
                setIsPlaying(false);
            } else {
                // Start playback
                console.log('Playing sound', recordingUri);
                player.seekTo(0);
                player.play();
                setIsPlaying(true);
            }
        } catch (err) {
            console.error('Failed to play sound', err);
            setIsPlaying(false);
        }
    };

    const saveRecordingToS3 = async () => {
        if (!recordingUri || !voiceName.trim()) {
            Alert.alert(getTranslation('error'), getTranslation('pleaseEnterVoiceName'));
            return;
        }

        setIsUploading(true);
        try {
            const userIdentifier = await getUserIdCreateIfNot();
            if (!userIdentifier) {
                throw new Error('User identifier is null');
            }

            // Get presigned URL
            const locale = await getLocale();
            const result = await fetch(`${API_URL}/add_voice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_identifier: userIdentifier,
                    file_extension: 'm4a',
                    name: voiceName.trim(),
                    locale: locale,
                }),
            });

            const response = await result.json();
            if (response.status !== 'OK') {
                throw new Error('Failed to get presigned URL');
            }
            console.log('/add_voice response:', response);

            console.log('Uploading file to S3...', response.presigned_url, recordingUri);
            // Upload file using Expo FileSystem
            const uploadResult = await FileSystem.uploadAsync(response.presigned_url, recordingUri as string, {
                httpMethod: 'PUT',
                uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,
                headers: {
                    'Content-Type': ''
                }
            });

            if (uploadResult.status !== 200) {
                throw new Error('Failed to upload to S3');
            }

            // Trigger voice preview TODO: This might be unnecessary
            fetch(`${API_URL}/trigger_voice_preview`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            // Save to local storage
            const newRecording = {
                name: voiceName.trim(),
                voiceId: response.voice_id,
                isDeleted: false,
            };
            await saveRecordings(newRecording);

            // Create story reading with the new voice clone
            if (storyId) {
                const userIdentifier = await getUserId();
                const jsonBody = {
                    story_id: storyId,
                    voice_id: response.voice_id,
                    user_identifier: userIdentifier
                }
                const readingResult = await fetch(`${API_URL}/create_story_elevenlabs_tts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(jsonBody)
                });
                console.log('/create_story_elevenlabs_tts response:', readingResult);

                if (readingResult.ok) {
                    const readingJson = await readingResult.json();
                    if (readingJson.status === "OK") {
                        router.back();
                        router.back();
                        router.push(`/reading_generating?startingPercent=5&ttsId=${readingJson.tts_id}&storyId=${storyId}`);
                        return;
                    } else {
                        throw new Error('Failed to create story reading');
                    }
                } else {
                    throw new Error('Failed to create story reading');
                }
            } else {
                // No storyId, just go back to previous screen
                router.back();
            }
        } catch (error) {
            console.error(error);
            Alert.alert(getTranslation('error'), getTranslation('failedToCreateVoiceClone'));
        } finally {
            setIsUploading(false);
        }
    };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <SafeAreaView style={MAIN_STYLES.safeArea}>
                <View style={[MAIN_STYLES.mainView, { flex: 1 }]}>
                    {/* Fixed content that doesn't move with keyboard */}
                    <View style={{ flex: 1, paddingBottom: 140 }}>
                        <Text style={styles.title}>{getTranslation('reviewRecording')}</Text>
                        <Text style={styles.explanationText}>
                            {getTranslation('reviewRecordingExplanation')}
                        </Text>

                        {/* Large Play Button */}
                        <View style={styles.playButtonContainer}>
                            <Pressable
                                style={[styles.largePlayButton, isPlaying && styles.playingButton]}
                                onPress={playSound}
                            >
                                <Ionicons
                                    name={isPlaying ? "pause" : "play"}
                                    size={80}
                                    color="white"
                                    style={!isPlaying && { marginLeft: 8 }} // Offset play icon to center it visually
                                />
                            </Pressable>
                            <Text style={styles.playButtonLabel}>
                                {isPlaying ? getTranslation('tapToPause') : getTranslation('tapToPlay')}
                            </Text>
                        </View>
                    </View>

                    {/* Voice Name Input and Button - Keyboard Avoiding */}
                    <KeyboardAvoidingView
                        behavior={Platform.OS === "ios" ? "padding" : "height"}
                        keyboardVerticalOffset={Platform.OS === "ios" ? 128 : 20}
                        style={styles.bottomInputSection}
                    >
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>{getTranslation('nameOfTheVoice')}</Text>
                            <TextInput
                                style={styles.input}
                                placeholder={getTranslation('enterVoiceName')}
                                value={voiceName}
                                onChangeText={setVoiceName}
                                placeholderTextColor="#EBEBF599"
                                editable={!isUploading}
                            />
                        </View>

                        {/* Continue Button */}
                        <Pressable
                            style={[
                                styles.continueButton,
                                isUploading && styles.buttonDisabled
                            ]}
                            onPress={saveRecordingToS3}
                            disabled={isUploading}>
                            {isUploading ? (
                                <ActivityIndicator color="white" size="small" />
                            ) : (
                                <>
                                    <Ionicons name="arrow-forward" size={24} color="white" />
                                    <Text style={styles.continueButtonText}>{getTranslation('createVoiceClone')}</Text>
                                </>
                            )}
                        </Pressable>
                    </KeyboardAvoidingView>
                </View>
            </SafeAreaView>
        </TouchableWithoutFeedback>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 32,
        fontWeight: '400',
        color: Colors['light'].text,
        textAlign: 'center',
        marginBottom: 16
    },
    explanationText: {
        fontSize: 18,
        color: '#EBEBF599',
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 32,
    },
    playButtonContainer: {
        alignItems: 'center',
        flex: 1,
        justifyContent: 'center',
    },
    largePlayButton: {
        width: 160,
        height: 160,
        borderRadius: 80,
        backgroundColor: Colors['light'].tint,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    playingButton: {
        backgroundColor: Colors['light'].stopRed,
    },
    playButtonLabel: {
        marginTop: 16,
        fontSize: 16,
        color: '#EBEBF599',
        textAlign: 'center',
    },
    inputContainer: {
        marginBottom: 24,
    },
    inputLabel: {
        fontSize: 16,
        color: Colors['light'].text,
        marginBottom: 8,
        fontWeight: '500',
    },
    input: {
        height: 56,
        backgroundColor: '#FFFFFF0D',
        borderRadius: 16,
        paddingHorizontal: 16,
        color: Colors['light'].text,
        fontSize: 16,
        borderWidth: 1,
        borderColor: '#FFFFFF20',
    },
    continueButton: {
        backgroundColor: Colors['light'].tint,
        padding: 18,
        borderRadius: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        opacity: 1,
    },
    continueButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    buttonDisabled: {
        opacity: 0.5,
    },
    bottomInputSection: {
        position: 'absolute',
        bottom: 16,
        left: 16,
        right: 16,
        paddingBottom: 16,
        backgroundColor: Colors['light'].background,
        paddingTop: 16,
    },
});