import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { View, Text, SafeAreaView, StyleSheet, Pressable, Alert, Platform, KeyboardAvoidingView, TouchableWithoutFeedback, Keyboard, ScrollView } from "react-native";
import { useEffect } from "react";
import Ionicons from '@expo/vector-icons/Ionicons';
import {
    useAudioRecorder,
    AudioModule,
    RecordingPresets,
    setAudioModeAsync,
    useAudioRecorderState,
} from 'expo-audio';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { getTranslation, getTranslationWithParams } from '@/components/util/LocalizationUtil';

export default function NewCloneScreen() {
    const router = useRouter();
    const { storyId } = useLocalSearchParams();
    const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
    const recorderState = useAudioRecorderState(audioRecorder);

    useEffect(() => {
        (async () => {
            setAudioModeAsync({
                playsInSilentMode: true,
                allowsRecording: true,
            });
        })();
    }, []);

    const startRecording = async () => {
        try {
            const audioPermissionStatus = await AudioModule.getRecordingPermissionsAsync();
            if (!audioPermissionStatus.granted) {
                console.log('Requesting permission..');
                const status = await AudioModule.requestRecordingPermissionsAsync();
                if (!status.granted) {
                    Alert.alert(getTranslation('microphonePermissionDenied'));
                    return;
                }
            }

            console.log('Starting recording..');
            await audioRecorder.prepareToRecordAsync();
            audioRecorder.record();
            console.log('Recording started');
        } catch (err) {
            console.error('Failed to start recording', err);
        }
    }

    async function stopRecording() {
        console.log('Stopping recording..');
        await audioRecorder.stop();
        const uri = audioRecorder.uri;
        console.log('Recording stopped and stored at', uri);

        // Navigate to review screen with recording URI and story ID
        router.push(`/review_recording?recordingUri=${encodeURIComponent(uri)}&storyId=${storyId || ''}`);
    }


    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <SafeAreaView style={MAIN_STYLES.safeArea}>
                <KeyboardAvoidingView
                    behavior={Platform.OS === "ios" ? "padding" : "height"}
                    style={[MAIN_STYLES.mainView, { flex: 1 }]}
                >
                    <View style={{ flex: 1 }}>
                        <Text style={styles.title}>{getTranslation('recordVoice')}</Text>
                        <Text style={styles.explanationText}>{getTranslation('recordInstructions')}</Text>
                        <ScrollView style={{ flex: 1 }}>
                            <Text style={styles.storyText}>{getTranslation('storyExample')}</Text>
                        </ScrollView>
                    </View>

                    <View style={styles.buttonContainer}>
                        <Pressable
                            style={[
                                styles.button,
                                recorderState.isRecording && { backgroundColor: Colors['light'].stopRed }
                            ]}
                            onPress={recorderState.isRecording ? stopRecording : startRecording}
                        >
                            <Ionicons name={recorderState.isRecording ? "stop-circle-outline" : "mic"} size={24} color="white" />
                            <Text style={styles.buttonText}>
                                {recorderState.isRecording ?
                                    getTranslationWithParams('finishRecording', { 0: Math.floor(recorderState.durationMillis / 1000) || 0 })
                                    : getTranslation('startRecording')}
                            </Text>
                        </Pressable>
                    </View>
                </KeyboardAvoidingView>
            </SafeAreaView>
        </TouchableWithoutFeedback>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 32,
        fontWeight: '400',
        color: Colors['light'].text,
        textAlign: 'center',
        marginBottom: 16
    },
    explanationText: {
        fontSize: 16,
        color: '#EBEBF599',
        marginBottom: 32,
        textAlign: 'center'
    },
    storyText: {
        fontSize: 16,
        color: Colors['light'].text
    },
    button: {
        backgroundColor: Colors['light'].tint,
        padding: 16,
        borderRadius: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
    },
    buttonText: {
        color: Colors['light'].text,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        marginLeft: 8
    },
    buttonContainer: {
        paddingVertical: 16,
        backgroundColor: Colors['light'].background,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center'
    },
});
