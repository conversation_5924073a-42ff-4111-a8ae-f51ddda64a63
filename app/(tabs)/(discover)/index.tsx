import { StyleSheet, View, SafeAreaView, TouchableOpacity, Text, ScrollView, Image, Dimensions, Pressable, ActivityIndicator, Alert } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { MAIN_STYLES } from "@/constants/Styles";
import { Colors } from "@/constants/Colors";
import { useEffect, useState } from "react";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import Ionicons from "@expo/vector-icons/Ionicons";
import { FlatGrid } from "react-native-super-grid";
import { router } from "expo-router";
import { StoryItem, getFavStoryIds, getStoryDuration, loadCuratedStories, loadStories, saveCuratedStories } from "@/components/util/StoryUtil";
import LocalPersistentCacheImage from "@/components/LocalPersistentCacheImage";
import { MINI_PLAYER_HEIGHT, S3_URL_STORY_IMAGES } from "@/constants/Constants";
import { useNavigation } from 'expo-router';
import { getChildProfile } from '@/components/util/ChildProfileUtil';
import { getLocale, getTranslation, getTranslationWithParams } from '@/components/util/LocalizationUtil';
import { API_URL } from '@/constants/Constants';
import { displayPaywall } from '@/components/util/AdaptyUtil';
import { getSubscriptionStatus } from '@/components/util/AdaptyUtil';
import { getIsOnboardingCompleted, setIsDevMode } from '@/components/util/GeneralUtil';

export default function HomeScreen() {
  const navigation = useNavigation();
  const storyWidth = (Dimensions.get('window').width - 32 - 16) / 2;
  const [activeFilter, setActiveFilter] = useState(0);
  const [createdStories, setCreatedStories] = useState<StoryItem[]>([]);
  const [curatedStories, setCuratedStories] = useState<StoryItem[]>([]);
  const [isCuratedStoriesLoading, setIsCuratedStoriesLoading] = useState(false);
  const [favoriteStories, setFavoriteStories] = useState<number[]>([]);
  const [childName, setChildName] = useState('');
  const [clickCounter, setClickCounter] = useState(0);

  useEffect(() => {
    const getChildName = async () => {
      const childProfile = await getChildProfile();
      if (childProfile) {
        setChildName(childProfile.name);
      }
    }
    const showPaywallIfNotSubscribed = async () => {
      const isActive = await getSubscriptionStatus();
      const isOnboardingCompleted = await getIsOnboardingCompleted();
      if (!isActive && isOnboardingCompleted) {
        await displayPaywall('general');
      }
    }
    getChildName();
    showPaywallIfNotSubscribed();
  }, []);

  const loadAndSetStories = async () => {
    const stories = await loadStories();
    setCreatedStories([...stories].reverse());
  }

  const loadAndSetFavStories = async () => {
    const favIds = await getFavStoryIds();
    setFavoriteStories(favIds);
  }

  const getCuratedStories = async () => {
    setIsCuratedStoriesLoading(true);
    try {
      const locale = getLocale();
      const existingCuratedStories = await loadCuratedStories();
      const textUpdateDict = existingCuratedStories.reduce((acc, s) => {
        acc[s.id] = s.textLastUpdateTime;
        return acc;
      }, {} as Record<string, typeof existingCuratedStories[number]['textLastUpdateTime']>);

      const result = await fetch(`${API_URL}/get_curated_stories_app/${locale}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text_updates: textUpdateDict })
      })
      
      const data = await result.json();

      if (data.status === 'OK') {
        await saveCuratedStories(data.stories);
      }
    } catch (e) {
      console.error("get_curated_stories_app error: ", e);
    } finally {
      const curatedStories = await loadCuratedStories();
      setCuratedStories(curatedStories);
      setIsCuratedStoriesLoading(false);
    }
  }

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadAndSetStories();
      loadAndSetFavStories();
    });

    // Initial load
    loadAndSetStories();
    loadAndSetFavStories();
    getCuratedStories();
    return () => {
      unsubscribe();
    };
  }, []);

  const devModeClick = () => {
    setClickCounter(clickCounter + 1);
    if (clickCounter > 10) {
      setIsDevMode(true);
      Alert.alert('Dev mode enabled');
    }
  }

  const FilterButton = ({ title, isActive, index, icon }) => (
    <TouchableOpacity style={[styles.filterButton, isActive && styles.activeFilterButton]}
      onPress={() => setActiveFilter(index)}>
      {icon}
      <Text style={styles.filterButtonText}>{title}</Text>
    </TouchableOpacity>
  );

  const StoryCard = ({ story }: { story: StoryItem }) => (
    <TouchableOpacity style={[styles.storyCard, { width: storyWidth, height: storyWidth + 48 }]}
      onPress={() => {
        router.push(`/read_story?id=${story.id}&curated=${story.curated ? 1 : 0}&imageLastUpdateTime=${story.imageLastUpdateTime}`);
      }}>
      <View style={[styles.thumbnail, { position: 'relative', width: storyWidth, height: storyWidth }]}>
        <LocalPersistentCacheImage
          url={`${S3_URL_STORY_IMAGES}/image_${story.id}.webp${story.imageLastUpdateTime ? `?cache=${story.imageLastUpdateTime}` : ''}`}
          style={[styles.thumbnail, { width: storyWidth, height: storyWidth }]} />
        {favoriteStories.includes(story.id) &&
          <View style={styles.bookmarkContainer}>
            <Ionicons name="heart" size={20} color={Colors['light'].likeRed} />
          </View>
        }
      </View>
      <Text
        numberOfLines={1}
        ellipsizeMode="tail"
        style={styles.storyTitle}
      >{story.title}</Text>
      <Text style={styles.storyMeta}>
        {getTranslationWithParams('min', { min: getStoryDuration(story.storyText) })}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={MAIN_STYLES.safeArea}>
      <View style={[MAIN_STYLES.mainView, { paddingHorizontal: 0, paddingBottom: 0 }]}>
        <Pressable onPress={devModeClick}>
          <ThemedText type="h1" style={{ marginLeft: 16 }}>{getTranslation('discover')}</ThemedText>
        </Pressable>

        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
          <FilterButton title={getTranslation('all')} isActive={activeFilter === 0} index={0}
            icon={<MaterialCommunityIcons name="dots-grid" size={18} color={Colors['light'].text} />} />
          <FilterButton title={childName === '' ? getTranslation('yourStories') : getTranslationWithParams('mainCharacter', { 0: childName })} isActive={activeFilter === 1} index={1}
            icon={<MaterialCommunityIcons name="library" size={18} color={Colors['light'].text} />} />
          <FilterButton title={getTranslation('new')} isActive={activeFilter === 2} index={2}
            icon={<Ionicons name="sparkles" size={16} color={Colors['light'].text} />} />
          <FilterButton title={getTranslation('favorites')} isActive={activeFilter === 3} index={3}
            icon={<Ionicons name="heart" size={18} color={Colors['light'].text} />} />
        </ScrollView>

        {isCuratedStoriesLoading ?
          <View style={styles.noStoriesContainer}>
            <ActivityIndicator size="large" color={Colors['light'].text} />
          </View>
          :
          <>
            <FlatGrid
              data={
                activeFilter === 1 ?
                  createdStories
                  : activeFilter === 2 ?
                    curatedStories
                    : activeFilter === 3 ?
                      [...createdStories, ...curatedStories].filter(s => favoriteStories.includes(s.id))
                      : [...createdStories, ...curatedStories]}
              itemDimension={storyWidth}
              spacing={16}
              showsVerticalScrollIndicator={false}
              renderItem={({ item }) => <StoryCard story={item} />}
              style={{ flex: 1 }}
              contentContainerStyle={{ paddingBottom: MINI_PLAYER_HEIGHT + 16 }}
              fixed={true}
            />
            {activeFilter === 1 && createdStories.length === 0 &&
              <View style={styles.noStoriesContainer}>
                <ThemedText style={styles.noStoriesText}>{getTranslation('noStories')}</ThemedText>
                <Pressable style={styles.noStoriesButton}
                  onPress={() => {
                    router.push('/(new_story)')
                  }}>
                  <ThemedText style={styles.noStoriesButtonText}>{getTranslation('createStory')}</ThemedText>
                </Pressable>
              </View>
            }
            {activeFilter === 3 && favoriteStories.length === 0 &&
              <View style={styles.noStoriesContainer}>
                <Ionicons name="heart" size={48} color={Colors['light'].likeRed} />
                <ThemedText type="subtitle">{getTranslation('noFavorites')}</ThemedText>
                <ThemedText style={styles.noStoriesText}>{getTranslation('addFavoritesHint')}</ThemedText>
              </View>
            }
          </>
        }
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  filtersContainer: {
    flexDirection: 'row',
    paddingLeft: 16,
    paddingBottom: 8,
    height: 48,
    flex: 0,
    maxHeight: 48
  },
  filterButton: {
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#21283F',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center'
  },
  activeFilterButton: {
    backgroundColor: Colors['light'].tint
  },
  filterButtonText: {
    color: Colors['light'].text,
    fontWeight: "400",
    fontSize: 16,
    marginLeft: 4,
    justifyContent: 'center',
    alignItems: 'center'
  },
  storiesContainer: {
    flex: 1,
  },
  storyCard: {
  },
  thumbnail: {
    width: '100%',
    borderRadius: 12,
    marginBottom: 4
  },
  storyTitle: {
    color: Colors.light.text,
    fontSize: 16,
    fontWeight: "400",
    marginBottom: 4,
  },
  storyMeta: {
    color: '#EBEBF599',
    fontSize: 14,
  },
  noStoriesContainer: {
    flex: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  noStoriesText: {
    color: Colors.light.text,
    fontSize: 16,
    fontWeight: "400",
  },
  noStoriesButton: {
    backgroundColor: '#4870FF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    marginTop: 16
  },
  noStoriesButtonText: {
    color: Colors.light.text,
    fontSize: 16,
    fontWeight: "400",
  },
  bookmarkContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1000,
    backgroundColor: 'rgba(255, 255, 255, 1)',
    padding: 6,
    borderRadius: 8,
  },
});
