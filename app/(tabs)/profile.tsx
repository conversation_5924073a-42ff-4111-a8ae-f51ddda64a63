import { StyleSheet, SafeAreaView, View, TouchableOpacity, Text, Linking, Platform, Modal, TextInput, KeyboardAvoidingView, Pressable, Keyboard, ActivityIndicator, Alert, Animated, ScrollView, Image, Dimensions } from 'react-native';
import { MAIN_STYLES } from '@/constants/Styles';
import { ThemedText } from '@/components/ThemedText';
import { getTranslation, getTranslationWithParams } from '@/components/util/LocalizationUtil';
import Ionicons from '@expo/vector-icons/build/Ionicons';
import { Colors } from '@/constants/Colors';
import { getClonePreviewUrl, IOS_WRITE_REVIEW_URL, IOS_WRITE_REVIEW_URL_BROWSER, PRIVACY_POLICY_URL, SLACK_FEEDBACK_URL, TERMS_OF_SERVICE_URL, API_URL } from '@/constants/Constants';
import { useState, useEffect } from 'react';
import { getUserId } from '@/components/util/LoginUtil';
import { displayPaywall, getCreditPrice, getSubscriptionStatus, purchaseCredit } from '@/components/util/AdaptyUtil';
import { getIsDevMode, resetIsDevMode, resetIsOnboardingCompleted } from '@/components/util/GeneralUtil';
import { deleteRecording, loadRecordings } from '@/components/util/RecordingUtil';
import { CloneImagePlaceholder } from '@/components/commonComponents/readStory/CloneImagePlaceholder';
import { useAudioPlayer } from 'expo-audio';
import { useRef } from 'react';
import BottomSheet from '@/components/BottomSheet';
import * as Clipboard from 'expo-clipboard';

export default function TabTwoScreen() {
  const [modalVisible, setModalVisible] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const glowValue = useState(new Animated.Value(0))[0];
  const [isLoadingPaywall, setIsLoadingPaywall] = useState(false);
  const [isDevMode, setIsDevMode] = useState(false);
  const [isVoicesModalVisible, setIsVoicesModalVisible] = useState(false);
  const [isLoadingCreditPurchase, setIsLoadingCreditPurchase] = useState(false);
  const [clones, setClones] = useState([]);
  const [userId, setUserId] = useState('');
  const [isPro, setIsPro] = useState(false);
  const [creditCount, setCreditCount] = useState(0);
  const [subscriptionRefreshDate, setSubscriptionRefreshDate] = useState('');
  const [creditPriceString, setCreditPriceString] = useState('');

  const initScreen = async () => {
    const isPro = await getSubscriptionStatus();
    setIsPro(isPro);

    const t = await getIsDevMode();
    setIsDevMode(t);

    const userId = await getUserId();
    setUserId(userId ? userId : '');

    if (isPro) {
      const creditPriceString = await getCreditPrice();
      setCreditPriceString(creditPriceString);

      try {
        const response = await fetch(`${API_URL}/get_user_credits/${userId}`);
        const data = await response.json();
        console.log(data);
        if (data.status === "OK") {
          setCreditCount(data.total_credits);
          setSubscriptionRefreshDate(data.credit_refresh_date.split(' ')[0]);
        }
      } catch (error) {
        console.error('Error fetching user credits:', error);
      }
    }
  }

  useEffect(() => {
    initScreen();
    loadClones();
  }, []);

  const loadClones = async () => {
    const loadedClones = await loadRecordings();
    setClones(loadedClones);
  };

  const SettingsRow = ({ text, action, icon, color }: { text: string, action: () => void, icon: React.ReactNode, color: string }) => {
    return (
      <TouchableOpacity style={styles.settingsRow} onPress={action}>
        <View style={[styles.settingsRowIconContainer, { backgroundColor: color }]}>
          {icon}
        </View>
        <Text style={styles.settingsRowText}>{text}</Text>
      </TouchableOpacity>
    )
  };

  const handleSubmitFeedback = async () => {
    setIsSubmitting(true);
    try {
      const userId = await getUserId();
      if (!userId) {
        throw new Error('User ID not found');
      }

      const response = await fetch(SLACK_FEEDBACK_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          type: "mrkdwn",
          text: `*User ID: <https://app.adapty.io/profiles/users?filter%5Bsearch%5D=${userId}|${userId}> (${Platform.OS === "ios" ? "iPhone" : "Android"})*\n>${feedback}`
        })
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }

      Alert.alert(
        getTranslation('success'),
        getTranslation('feedbackSubmitted'),
        [{ text: getTranslation('ok') }]
      );

      setFeedback('');
      setModalVisible(false);
    } catch (error) {
      Alert.alert(
        getTranslation('error'),
        getTranslation('feedbackError'),
        [{ text: getTranslation('ok') }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const startGlowAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(glowValue, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(glowValue, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const handleGetPremium = async () => {
    setIsLoadingPaywall(true);
    try {
      await displayPaywall("general");
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoadingPaywall(false);
    }
  };

  useEffect(() => {
    startGlowAnimation();
  }, []);

  const CloneRow = ({ clone }) => {
    const player = useAudioPlayer(getClonePreviewUrl(clone.voiceId));
    const [isPlaying, setIsPlaying] = useState(false);
    const playbackStatusRef = useRef<NodeJS.Timeout | null>(null);

    const playSound = async () => {
      try {
        if (isPlaying) return;

        setIsPlaying(true);
        player.play();

        // Custom playback monitoring solution
        const checkPlaybackStatus = () => {
          if (player.duration && player.currentTime >= player.duration) {
            setIsPlaying(false);
            if (playbackStatusRef.current) {
              clearInterval(playbackStatusRef.current);
              playbackStatusRef.current = null;
            }
          }
        };

        playbackStatusRef.current = setInterval(checkPlaybackStatus, 100);
      } catch (error) {
        console.error('Error playing sound:', error);
        setIsPlaying(false);
      }
    };

    useEffect(() => {
      return () => {
        if (playbackStatusRef.current) {
          clearInterval(playbackStatusRef.current);
        }
        player.pause();
      };
    }, [player]);

    return (
      <View style={styles.voiceSelectionRow}>
        <CloneImagePlaceholder name={clone.name} voiceId={clone.voiceId} />
        <Pressable
          style={styles.voicePreviewButton}
          onPress={playSound}
        >
          {isPlaying ? (
            <ActivityIndicator size="small" color={Colors['light'].text} />
          ) : (
            <Ionicons
              name={"play"}
              size={16}
              color={Colors['light'].text}
            />
          )}
        </Pressable>
        <View style={MAIN_STYLES.col}>
          <Text style={styles.voiceNameText}>{clone.name}</Text>
        </View>
        <View style={MAIN_STYLES.flex1} />
        <Pressable
          style={styles.deleteButton}
          onPress={() => {
            Alert.alert(
              getTranslation('deleteVoice'),
              getTranslation('deleteVoiceConfirmation'),
              [
                {
                  text: getTranslation('cancel'),
                  style: 'cancel'
                },
                {
                  text: getTranslation('delete'),
                  style: 'destructive',
                  onPress: async () => {
                    await deleteRecording(clone.voiceId);
                    await loadClones();
                  }
                }
              ]
            );
          }}
        >
          <Ionicons name="trash-outline" size={20} color={Colors['light'].text} />
        </Pressable>
      </View>
    );
  };

  return (
    <SafeAreaView style={MAIN_STYLES.safeArea}>
      <View style={MAIN_STYLES.mainView}>
        <ThemedText type="h1">{getTranslation('profile')}</ThemedText>

        {
          !isPro &&
          <View style={styles.premiumContainer}>
            <ThemedText type="h2" style={{ marginBottom: 16 }}>{getTranslation('taleAIPremium')}</ThemedText>
            <ThemedText type="semiBold" style={{ marginBottom: 8 }}>{getTranslation('premiumDescription1')}</ThemedText>
            <ThemedText type="semiBold" style={{ marginBottom: 8 }}>{getTranslation('premiumDescription2')}</ThemedText>
            <ThemedText type="semiBold" style={{ marginBottom: 8 }}>{getTranslation('premiumDescription3')}</ThemedText>

            <Animated.View style={{
              width: '100%',
              transform: [{
                scale: glowValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.02]
                })
              }],
              opacity: glowValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.8, 1]
              })
            }}>
              <TouchableOpacity
                style={styles.getPremiumButton}
                onPress={handleGetPremium}
                disabled={isLoadingPaywall}>
                {isLoadingPaywall ? (
                  <ActivityIndicator color={Colors['light'].text} size={24} />
                ) : (
                  <ThemedText type="semiBold" style={MAIN_STYLES.centerText}>
                    {getTranslation('getPremium')}
                  </ThemedText>
                )}
              </TouchableOpacity>
            </Animated.View>
          </View>
        }

        {
          isPro &&
          <View style={styles.premiumContainer}>
            <ThemedText type="h2" style={{ marginBottom: 16 }}>{getTranslation('profilePremiumTitle')}</ThemedText>
            <ThemedText type="semiBold" style={{ marginBottom: 8 }}>{getTranslation('currentCredits')}<ThemedText type='default'>{creditCount}</ThemedText></ThemedText>
            <ThemedText type="semiBold" style={{ marginBottom: 16 }}>{getTranslation('nextRefresh')}<ThemedText type='default'>{subscriptionRefreshDate}</ThemedText></ThemedText>

            <Animated.View style={{
              width: '100%',
              transform: [{
                scale: glowValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.02]
                })
              }],
              opacity: glowValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.8, 1]
              })
            }}>
              <TouchableOpacity
                style={[styles.getPremiumButton, { backgroundColor: Colors['light'].brightGreen, shadowColor: Colors['light'].brightGreen }]}
                onPress={async () => {
                  setIsLoadingCreditPurchase(true);
                  try {
                    await purchaseCredit((isSuccess) => {
                      if (isSuccess) {
                        setCreditCount(creditCount + 50);
                      }
                    });
                  } finally {
                    setIsLoadingCreditPurchase(false);
                  }
                }}
                disabled={isLoadingCreditPurchase}>
                {isLoadingCreditPurchase ? (
                  <ActivityIndicator color={Colors['light'].text} size={24} />
                ) : (
                  <>
                    <ThemedText type="semiBold" style={MAIN_STYLES.centerText}>
                      {getTranslation('getMoreCredits')}
                    </ThemedText>
                    <ThemedText type='default' style={MAIN_STYLES.centerText}>{creditPriceString}</ThemedText>
                  </>
                )}
              </TouchableOpacity>
            </Animated.View>
          </View>
        }

        <SettingsRow
          text={getTranslation('myVoices')}
          icon={<Ionicons name="mic" size={24} color={Colors['light'].text} />}
          color={Colors['light'].brightGreen}
          action={() => setIsVoicesModalVisible(true)}
        />
        <SettingsRow
          text={getTranslation('privacyPolicy')}
          action={() => {
            Linking.openURL(PRIVACY_POLICY_URL);
          }}
          icon={<Ionicons name="lock-closed" size={24} color={Colors['light'].text} />}
          color={Colors['light'].brightGreen} />
        <SettingsRow
          text={getTranslation('termsOfService')}
          action={() => {
            Linking.openURL(TERMS_OF_SERVICE_URL);
          }}
          icon={<Ionicons name="document-text" size={24} color={Colors['light'].text} />}
          color={Colors['light'].brightGreen} />
        <SettingsRow
          text={getTranslation('rateUs')}
          action={() => {
            if (Platform.OS === 'ios') {
              Linking.openURL(IOS_WRITE_REVIEW_URL).catch(() => {
                Linking.openURL(IOS_WRITE_REVIEW_URL_BROWSER);
              });
            } else {
              // TODO: Add Android rate us URL
            }
          }}
          icon={<Ionicons name="logo-apple-appstore" size={24} color={Colors['light'].text} />}
          color={Colors['light'].appStoreLightBlue} />
        <SettingsRow
          text={getTranslation('sendFeedback')}
          action={() => setModalVisible(true)}
          icon={<Ionicons name="chatbox-ellipses" size={24} color={Colors['light'].text} />}
          color={Colors['light'].brightRed} />
        {isDevMode && <>
          <ThemedText type="h3" style={{ marginBottom: 8, marginTop: 16 }}>{"Dev Mode:"}</ThemedText>
          <SettingsRow
            text={"Reset Onboarding"}
            action={() => resetIsOnboardingCompleted()}
            icon={<Ionicons name="refresh" size={24} color={Colors['light'].text} />}
            color={Colors['light'].gray} />
          <SettingsRow
            text={"Reset Dev Mode"}
            action={() => resetIsDevMode()}
            icon={<Ionicons name="refresh" size={24} color={Colors['light'].text} />}
            color={Colors['light'].gray} />
        </>}

        <View style={MAIN_STYLES.flex1} />
        <View style={styles.userIdContainer}>
          <ThemedText type="semiBold" style={{ textAlign: 'center' }}>{getTranslation('userId')}</ThemedText>
          <View style={styles.userIdRow}>
            <ThemedText type="default" style={{ textAlign: 'center' }}>{userId}</ThemedText>
            <TouchableOpacity
              onPress={() => {
                Clipboard.setStringAsync(userId);
                Alert.alert(getTranslation('success'), getTranslation('copied'));
              }}
              style={styles.copyButton}
            >
              <Ionicons name="copy-outline" size={20} color={Colors['light'].text} />
            </TouchableOpacity>
          </View>
        </View>

        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={MAIN_STYLES.flex1}>
            <Pressable style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
              <Pressable
                style={styles.modalContent}
                onPress={(e) => {
                  e.stopPropagation();
                  Keyboard.dismiss();
                }}>
                <ThemedText type="h2" style={{ marginBottom: 16 }}>{getTranslation('sendFeedback')}</ThemedText>
                <TextInput
                  style={styles.feedbackInput}
                  multiline
                  numberOfLines={4}
                  value={feedback}
                  onChangeText={setFeedback}
                  placeholder={getTranslation('feedbackPlaceholder')}
                  placeholderTextColor={Colors['light'].gray}
                />
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={[styles.button, styles.cancelButton]}
                    onPress={() => setModalVisible(false)}>
                    <Text style={styles.buttonText}>{getTranslation('cancel')}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.button, styles.submitButton]}
                    onPress={(e) => {
                      e.stopPropagation();
                      handleSubmitFeedback();
                    }}
                    disabled={isSubmitting}>
                    {isSubmitting ? (
                      <ActivityIndicator color={Colors['light'].text} />
                    ) : (
                      <Text style={styles.buttonText}>{getTranslation('submit')}</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </Pressable>
            </Pressable>
          </KeyboardAvoidingView>
        </Modal>

        <BottomSheet
          isVisible={isVoicesModalVisible}
          onClose={() => setIsVoicesModalVisible(false)}>
          <ScrollView
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.scrollViewContent}
          >
            <Text style={styles.modalTitle}>{getTranslation('myVoices')}</Text>
            {clones.filter((clone) => !clone.isDeleted).map((clone) => (
              <CloneRow key={`clone-${clone.voiceId}`} clone={clone} />
            ))}
          </ScrollView>
        </BottomSheet>
      </View>
    </SafeAreaView >
  );
}

const styles = StyleSheet.create({
  settingsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors['light'].gray
  },
  settingsRowIconContainer: {
    padding: 8,
    borderRadius: 10,
    marginRight: 8
  },
  settingsRowText: {
    flex: 1,
    textAlign: 'left',
    fontWeight: '700',
    fontSize: 16,
    color: Colors['light'].text
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors['light'].background,
    borderRadius: 20,
    padding: 16,
    width: '80%',
    alignItems: 'center',
  },
  feedbackInput: {
    width: '100%',
    borderWidth: 1,
    borderColor: Colors['light'].gray,
    borderRadius: 12,
    padding: 10,
    marginBottom: 16,
    textAlignVertical: 'top',
    color: Colors['light'].text,
    height: 120
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 8
  },
  button: {
    padding: 10,
    borderRadius: 12,
    flex: 1,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center'
  },
  cancelButton: {
    backgroundColor: Colors['light'].gray,
  },
  submitButton: {
    backgroundColor: Colors['light'].brightGreen,
  },
  buttonText: {
    color: Colors['light'].text,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  premiumContainer: {
    width: '100%',
    padding: 16,
    borderRadius: 16,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors['light'].blueCharcoal
  },
  getPremiumButton: {
    backgroundColor: Colors['light'].brightOrange,
    padding: 10,
    borderRadius: 16,
    width: '100%',
    marginTop: 16,
    shadowColor: Colors['light'].brightOrange,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 5,
  },
  bottomModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    marginTop: 'auto',
  },
  bottomModalContent: {
    backgroundColor: Colors['light'].tabBarBackground,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    paddingTop: 16,
    maxHeight: '60%',
    width: '100%'
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 20
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: Colors['light'].text,
    marginBottom: 16,
    textAlign: 'center'
  },
  voiceSelectionRow: {
    flexDirection: 'row',
    marginBottom: 8,
    borderBottomColor: '#2D344B',
    borderBottomWidth: 1,
    alignItems: 'center',
    paddingBottom: 8
  },
  voiceNameText: {
    color: Colors['light'].text,
    fontWeight: '500',
    fontSize: 18
  },
  deleteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors['light'].stopRed,
    justifyContent: 'center',
    alignItems: 'center',
  },
  newCloneButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 16,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16
  },
  newCloneButtonText: {
    color: Colors['light'].text,
    fontWeight: '500',
    fontSize: 16
  },
  voicePreviewButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.tint,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8
  },
  userIdContainer: {
    alignItems: 'center',
  },
  userIdRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  copyButton: {
    padding: 4
  }
});
