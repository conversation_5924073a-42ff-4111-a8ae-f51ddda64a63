import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import 'react-native-reanimated';
import { appLoginFlow } from '@/components/util/LoginUtil';
import { AudioProvider } from '@/contexts/AudioContext';
import { useColorScheme } from '@/hooks/useColorScheme';
// TODO: Migrate to Superwall - Remove these imports once Superwall is integrated
// import { adapty } from 'react-native-adapty';
// import { ADAPTY_PUBLIC_KEY } from '@/constants/Constants';
import { requestTrackingPermission } from '@/components/util/FacebookUtil';
import { SpecialVoicesProvider } from '@/contexts/SpecialVoicesContext';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet } from 'react-native';
import * as Font from 'expo-font';
import { adaptyIdentify } from '@/components/util/AdaptyUtil';
import { SuperwallProvider } from "expo-superwall";
import { SUPER_WALL_PUBLIC_KEY_iOS, SUPER_WALL_PUBLIC_KEY_Android } from '@/util/SuperWall';



// TODO: Migrate to Superwall - Initialize Superwall SDK here
// adapty.activate(ADAPTY_PUBLIC_KEY);

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    'Quicksand': require('../assets/fonts/Quicksand-VariableFont_wght.ttf'),
    'OpenSans-Italic': require('../assets/fonts/OpenSans-Italic-VariableFont_wdth,wght.ttf'),
    OpenSans: require('../assets/fonts/OpenSans-VariableFont_wdth,wght.ttf'),
  });

  const initFlow = async () => {
    await adaptyIdentify(); // Will be replaced with Superwall user identification
    await appLoginFlow();
    SplashScreen.hideAsync();
    await requestTrackingPermission();
  }

  useEffect(() => {
    if (loaded) {
      console.log('Loaded fonts:', Font.isLoaded('OpenSans'));
      initFlow();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <SuperwallProvider apiKeys={{ ios: SUPER_WALL_PUBLIC_KEY_iOS, android: SUPER_WALL_PUBLIC_KEY_Android }}>
        <AudioProvider>
          <SpecialVoicesProvider>
            <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
              <Stack>
                <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
                <Stack.Screen name="player" options={{
                  headerShown: true,
                  headerTransparent: true,
                  title: '',
                }} />
              </Stack>
            </ThemeProvider>
          </SpecialVoicesProvider>
          <StatusBar style="light" />
        </AudioProvider>
      </SuperwallProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
