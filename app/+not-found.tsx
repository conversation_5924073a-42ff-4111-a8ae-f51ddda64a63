import { Link, Stack } from 'expo-router';
import { StyleSheet } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { getTranslation } from '@/components/util/LocalizationUtil';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: getTranslation('oops') }} />
      <ThemedView style={styles.container}>
        <ThemedText type="title">{getTranslation('screenDoesNotExist')}</ThemedText>
        <Link href="/" style={styles.link}>
          <ThemedText type="link">{getTranslation('goToHomeScreen')}</ThemedText>
        </Link>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
});
