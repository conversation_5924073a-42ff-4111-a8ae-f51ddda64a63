import { MAIN_STYLES } from "@/constants/Styles";
import { View, SafeAreaView, Text, Image, Pressable, TouchableWithoutFeedback, KeyboardAvoidingView, Keyboard, ScrollView, TextInput, ActivityIndicator, Platform, Alert, StyleSheet } from "react-native";
import { onboardingStyles } from "./_layout";
import { getLocale, getTranslation, getTranslationWithParams } from "@/components/util/LocalizationUtil";
import { ThemedText } from "@/components/ThemedText";
import { useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { useEffect, useRef, useState } from "react";
import { useAudioRecorder, AudioModule, RecordingPresets, useAudioPlayer } from 'expo-audio';
import { saveRecordings } from "@/components/util/RecordingUtil";
import { loadRecordings } from "@/components/util/RecordingUtil";
import * as FileSystem from 'expo-file-system';
import { getUserIdCreateIfNot } from "@/components/util/LoginUtil";
import { API_URL } from "@/constants/Constants";
import Ionicons from "@expo/vector-icons/Ionicons";
import { setIsOnboardingCompleted } from "@/components/util/GeneralUtil";
import { displayPaywall } from "@/components/util/AdaptyUtil";

export default function OnboardingClone2() {
    const router = useRouter();
    const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
    const player = useAudioPlayer();
    const [recordingUri, setRecordingUri] = useState('');
    const [showNameInput, setShowNameInput] = useState(false);
    const [voiceName, setVoiceName] = useState('');
    const [isUploading, setIsUploading] = useState(false);
    const [recordingTime, setRecordingTime] = useState(0);
    const timerRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [hasPermission, setHasPermission] = useState(false);

    useEffect(() => {
        checkPermissions();
    }, []);

    const checkPermissions = async () => {
        const status = await AudioModule.getRecordingPermissionsAsync();
        setHasPermission(status.granted);
    };

    async function startRecording() {
        try {
            if (!hasPermission) {
                console.log('Requesting permission..');
                const status = await AudioModule.requestRecordingPermissionsAsync();
                if (!status.granted) {
                    Alert.alert('Permission to access microphone was denied');
                    return;
                }
                setHasPermission(true);
            }

            console.log('Starting recording..');
            await audioRecorder.prepareToRecordAsync();
            audioRecorder.record();

            setRecordingTime(0);
            timerRef.current = setInterval(() => {
                setRecordingTime((prev) => prev + 1);
            }, 1000);

            console.log('Recording started');
        } catch (err) {
            console.error('Failed to start recording', err);
        }
    }

    async function stopRecording() {
        console.log('Stopping recording..');
        await audioRecorder.stop();
        const uri = audioRecorder.uri;
        setRecordingUri(uri);
        console.log('Recording stopped and stored at', uri);

        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }
    }

    async function playSound() {
        try {
            setIsPlaying(true);
            await player.replace({ uri: recordingUri });
            player.play();

            // Monitor playback
            const checkPlayback = setInterval(() => {
                if (!player.playing) {
                    setIsPlaying(false);
                    clearInterval(checkPlayback);
                }
            }, 100);
        } catch (err) {
            console.error('Failed to play sound', err);
            setIsPlaying(false);
        }
    }

    const saveRecordingToS3 = async () => {
        if (!recordingUri || !voiceName) return;

        setIsUploading(true);
        try {
            const userIdentifier = await getUserIdCreateIfNot();
            if (!userIdentifier) {
                throw new Error('User identifier is null');
            }

            // Get presigned URL
            const locale = await getLocale();
            const result = await fetch(`${API_URL}/add_voice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_identifier: userIdentifier,
                    file_extension: 'm4a',
                    locale: locale,
                    name: voiceName,
                }),
            });

            const response = await result.json();
            if (response.status !== 'OK') {
                throw new Error('Failed to get presigned URL');
            }

            console.log('Uploading file to S3...', response.presigned_url, recordingUri);
            // Upload file using Expo FileSystem
            const uploadResult = await FileSystem.uploadAsync(response.presigned_url, recordingUri, {
                httpMethod: 'PUT',
                uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,
                headers: {
                    'Content-Type': ''
                }
            });

            if (uploadResult.status !== 200) {
                throw new Error('Failed to upload to S3');
            }

            // Trigger voice preview
            fetch(`${API_URL}/trigger_voice_preview`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            // Save to local storage
            const newRecording = {
                name: voiceName,
                voiceId: response.voice_id,
                isDeleted: false,
            };
            await saveRecordings(newRecording);
            router.push(`/clone3?voiceId=${response.voice_id}&name=${voiceName}`);

        } catch (error) {
            console.error(error);
            Alert.alert('Error', 'Failed to create voice clone.');
        } finally {
            setIsUploading(false);
        }
    };

    return <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={MAIN_STYLES.safeArea}>
            <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                style={[MAIN_STYLES.mainView, { flex: 1 }]}
            >
                <View style={{ flex: 1 }}>
                    <ThemedText type="h1" style={{ textAlign: 'center' }}>{getTranslation('recordVoice')}</ThemedText>
                    <Text style={onboardingStyles.subTitle}>{getTranslation('recordInstructions')}</Text>
                    <ScrollView style={{ flex: 1, marginTop: 16, backgroundColor: Colors.light.blueCharcoal, borderRadius: 8, paddingHorizontal: 8, paddingVertical: 16 }}>
                        <Text style={styles.storyText}>{getTranslation('storyExample')}</Text>
                    </ScrollView>
                </View>

                <View style={[styles.buttonContainer]}>
                    {recordingUri === "" ? (
                        <View style={{ flexDirection: 'column', width: '100%' }}>
                            <Pressable
                                style={[
                                    styles.button,
                                    { backgroundColor: Colors.light.tint },
                                    audioRecorder.recording && { backgroundColor: Colors['light'].stopRed }
                                ]}
                                onPress={audioRecorder.recording ? stopRecording : startRecording}
                            >
                                <Ionicons name={audioRecorder.recording ? "stop-circle-outline" : "mic"} size={24} color="white" />
                                <Text style={styles.buttonText}>
                                    {audioRecorder.recording ? getTranslationWithParams('finishRecording', { 0: recordingTime }) : getTranslation('startRecording')}
                                </Text>
                            </Pressable>
                            <Pressable style={[onboardingStyles.onboardingButton, { backgroundColor: Colors.light.blueCharcoal, marginTop: 8, width: '100%' }]} onPress={() => {
                                router.push('/(tabs)/(discover)');
                                // displayPaywall('general');
                                setIsOnboardingCompleted();
                            }}>
                                <Text style={[MAIN_STYLES.mainButtonText]}>{getTranslation('skipThisStep')}</Text>
                            </Pressable>
                        </View>
                    ) : (
                        !showNameInput ? (
                            <>
                                <Pressable style={styles.button} onPress={isPlaying ? async () => {
                                    player.pause();
                                    setIsPlaying(false);
                                } : playSound}>
                                    <Ionicons
                                        name={isPlaying ? "stop" : "play"}
                                        size={24}
                                        color="white"
                                    />
                                </Pressable>
                                <Pressable
                                    style={[styles.button, { backgroundColor: Colors['light'].stopRed }]}
                                    onPress={async () => {
                                        if (player.playing) {
                                            player.pause();
                                            setIsPlaying(false);
                                        }
                                        setRecordingUri('');
                                    }}>
                                    <Ionicons name="refresh" size={24} color="white" />
                                </Pressable>
                                <Pressable
                                    style={[styles.button, { backgroundColor: Colors['light'].tint, flex: 1 }]}
                                    onPress={async () => {
                                        if (player.playing) {
                                            player.pause();
                                            setIsPlaying(false);
                                        }
                                        setShowNameInput(true)
                                    }}>
                                    <Ionicons name="arrow-forward" size={24} color="white" />
                                    <Text style={styles.buttonText}>{getTranslation('continue')}</Text>
                                </Pressable>
                            </>
                        ) : (
                            <>
                                <TextInput
                                    style={styles.input}
                                    placeholder="Name of the Voice"
                                    value={voiceName}
                                    onChangeText={setVoiceName}
                                    placeholderTextColor="#EBEBF599"
                                    editable={!isUploading}
                                />
                                <Pressable
                                    style={[
                                        styles.button,
                                        { backgroundColor: Colors['light'].tint },
                                        isUploading && styles.buttonDisabled
                                    ]}
                                    onPress={saveRecordingToS3}
                                    disabled={isUploading}>
                                    {isUploading ? (
                                        <ActivityIndicator color="white" />
                                    ) : (
                                        <Ionicons name="arrow-forward" size={24} color="white" />
                                    )}
                                </Pressable>
                            </>
                        )
                    )}
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    </TouchableWithoutFeedback>;
}

const styles = StyleSheet.create({
    title: {
        fontSize: 32,
        fontWeight: '400',
        color: Colors['light'].text,
        textAlign: 'center',
        marginBottom: 16
    },
    explanationText: {
        fontSize: 16,
        color: '#EBEBF599',
        marginBottom: 32,
        textAlign: 'center'
    },
    storyText: {
        fontSize: 16,
        color: Colors['light'].text
    },
    button: {
        backgroundColor: Colors['light'].tint,
        padding: 16,
        borderRadius: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        color: Colors['light'].text,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        marginLeft: 8
    },
    buttonContainer: {
        gap: 12,
        flexDirection: 'row',
        paddingVertical: 16,
        backgroundColor: Colors['light'].background,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center'
    },
    input: {
        flex: 1,
        height: 50,
        backgroundColor: '#FFFFFF0D',
        borderRadius: 16,
        paddingHorizontal: 16,
        color: Colors['light'].text,
        fontSize: 16,
    },
    buttonDisabled: {
        opacity: 0.5,
    },
});