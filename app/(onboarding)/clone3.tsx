import { MAIN_STYLES } from "@/constants/Styles";
import { View, SafeAreaView, Text, Image, Pressable, Alert } from "react-native";
import { onboardingStyles } from "./_layout";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { ThemedText } from "@/components/ThemedText";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { AISphere } from "@/components/AISphere";
import { useState, useEffect } from "react";
import { API_URL } from "@/constants/Constants";
import { setIsOnboardingCompleted } from "@/components/util/GeneralUtil";
import { displayPaywall } from "@/components/util/AdaptyUtil";
import TypingText from "@/components/TypingText";

export default function OnboardingClone3() {
    const router = useRouter();
    const { voiceId, name } = useLocalSearchParams();
    const [generatingPercentage, setGeneratingPercentage] = useState(1);

    useEffect(() => {
        const pollStatus = async () => {
            try {
                const response = await fetch(`${API_URL}/get_clone_preview_status/${voiceId}`);
                const data = await response.json();
                console.log('Clone preview status:', data);
                if (data.status === "OK") {
                    if (data.preview_status.is_error === 1) {
                        clearInterval(pollingInterval); // Clear interval on error
                        Alert.alert('Error', 'Failed to clone voice. Please try again later.');
                        router.push('/(tabs)/(discover)');
                        // displayPaywall('general');
                        setIsOnboardingCompleted();
                    } else if (data.preview_status.is_preview_created === 1) {
                        clearInterval(pollingInterval); // Clear interval on success
                        router.push(`/clone4?voiceId=${voiceId}&name=${name}`);
                    }
                }
            } catch (error) {
                console.error('Error polling clone status:', error);
            }
        };

        const pollingInterval = setInterval(pollStatus, 5000); // Poll every 5 seconds

        // Initial poll
        pollStatus();

        return () => clearInterval(pollingInterval);
    }, [voiceId]);

    useEffect(() => {
        const duration = 120 * 1000; // 2 minutes in milliseconds
        const intervalTime = 1000; // Update every second
        const totalSteps = duration / intervalTime;
        const baseIncrement = 98 / totalSteps; // Total range (99-1) divided by steps

        const interval = setInterval(() => {
            setGeneratingPercentage(current => {
                if (current >= 99) {
                    clearInterval(interval);
                    return 99;
                }
                // Add random variation of ±20% to the base increment
                const variation = baseIncrement * (0.8 + Math.random() * 0.4);
                return Math.min(99, current + variation);
            });
        }, intervalTime);

        return () => clearInterval(interval);
    }, []);

    const getProgressMessage = () => {
        const generatingTexts = Array.from({ length: 4 }, (_, i) => 
            getTranslation(`${'generatingClone'}${i + 1}`)
        );
        
        // Calculate which text to show based on current progress
        const segmentSize = 100 / generatingTexts.length;
        const index = Math.min(
            Math.floor(generatingPercentage / segmentSize),
            generatingTexts.length - 1
        );
        
        return generatingTexts[index];
    };

    return <SafeAreaView style={MAIN_STYLES.safeArea}>
        <View style={onboardingStyles.mainView}>
            <View style={MAIN_STYLES.flex1} />
            <AISphere />
            <Text style={onboardingStyles.title}>{getTranslation('onboardingClone3')}{"\n"}{Math.round(generatingPercentage)}%</Text>
            <TypingText text={getProgressMessage()} />
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={[onboardingStyles.onboardingButton, { backgroundColor: Colors.light.blueCharcoal, marginTop: 8 }]} onPress={() => {
                router.push('/(tabs)/(discover)');
                // displayPaywall('general');
                setIsOnboardingCompleted();
            }}>
                <Text style={[MAIN_STYLES.mainButtonText]}>{getTranslation('skipThisStep')}</Text>
            </Pressable>
        </View>
    </SafeAreaView>;
}