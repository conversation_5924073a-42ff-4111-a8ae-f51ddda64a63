import { MAIN_STYLES } from "@/constants/Styles";
import { View, SafeAreaView, Text, Image, Pressable, Alert, StyleSheet } from "react-native";
import { onboardingStyles } from "./_layout";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { ThemedText } from "@/components/ThemedText";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { AISphere } from "@/components/AISphere";
import { useState, useEffect, useRef } from "react";
import { API_URL, getClonePreviewUrl } from "@/constants/Constants";
import { useAudioPlayer } from 'expo-audio';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
    useAnimatedStyle,
    withRepeat,
    withTiming,
    withDelay,
    withSequence,
} from 'react-native-reanimated';
import { displayPaywall } from "@/components/util/AdaptyUtil";
import { setIsOnboardingCompleted } from "@/components/util/GeneralUtil";

export default function OnboardingClone3() {
    const router = useRouter();
    const { voiceId, name } = useLocalSearchParams();
    const [isPlaying, setIsPlaying] = useState(false);
    const player = useAudioPlayer(getClonePreviewUrl(voiceId));
    const playbackStatusRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        // Monitor playback status
        const checkPlaybackStatus = () => {
            if (player.playing !== isPlaying) {
                setIsPlaying(player.playing);
            }
        };

        playbackStatusRef.current = setInterval(checkPlaybackStatus, 100);

        return () => {
            if (playbackStatusRef.current) {
                clearInterval(playbackStatusRef.current);
            }
        };
    }, [player.playing]);


    const playSound = async () => {
        try {
            if (isPlaying) {
                player.pause();
                player.seekTo(0);
                setIsPlaying(false);
            } else {
                player.seekTo(0);
                player.play();
                setIsPlaying(true);
            }
        } catch (error) {
            console.error('Error playing sound:', error);
            Alert.alert('Error', 'Failed to play audio');
        }
    };

    const createRingStyle = (delay: number) => useAnimatedStyle(() => ({
        opacity: withSequence(
            withDelay(
                delay,
                withTiming(isPlaying ? 0.7 : 0, { duration: 500 })
            ),
            withTiming(0, { duration: 1500 })
        ),
        transform: [
            {
                scale: withSequence(
                    withDelay(
                        delay,
                        withTiming(isPlaying ? 2 : 1, { duration: 2000 })
                    ),
                    withTiming(1, { duration: 0 })
                ),
            },
        ],
    }));

    const ring1Style = createRingStyle(0);
    const ring2Style = createRingStyle(400);
    const ring3Style = createRingStyle(800);

    return <SafeAreaView style={MAIN_STYLES.safeArea}>
        <View style={onboardingStyles.mainView}>
            <Text style={onboardingStyles.title}>{getTranslation('onboardingClone4')}</Text>
            <Text style={onboardingStyles.subTitle}>{getTranslation('onboardingClone4Subtitle')}</Text>

            <View style={styles.soundWaveContainer}>
                {isPlaying && (
                    <>
                        <Animated.View style={[styles.ring, ring1Style]} />
                        <Animated.View style={[styles.ring, ring2Style]} />
                        <Animated.View style={[styles.ring, ring3Style]} />
                    </>
                )}
                <Pressable
                    style={styles.playButton}
                    onPress={playSound}
                >
                    <Ionicons
                        name={isPlaying ? "pause" : "play"}
                        size={40}
                        color="white"
                    />
                </Pressable>
            </View>
            <Text style={[onboardingStyles.title, styles.nameText]}>{name}</Text>

            <View style={MAIN_STYLES.flex1} />
            <Pressable style={[onboardingStyles.onboardingButton]} onPress={() => {
                router.push('/(tabs)/(discover)');
                // displayPaywall('general');
                setIsOnboardingCompleted();
            }}>
                <Text style={[MAIN_STYLES.mainButtonText]}>{getTranslation('generateAIStories')}</Text>
            </Pressable>
        </View>
    </SafeAreaView>;
}

const styles = StyleSheet.create({
    playButton: {
        width: 100,
        height: 100,
        borderRadius: 50,
        backgroundColor: Colors.light.tint,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: Colors.light.tint,
        shadowOffset: {
            width: 0,
            height: 0,
        },
        shadowOpacity: 0.5,
        shadowRadius: 10,
        elevation: 5,
    },
    soundWaveContainer: {
        height: 180,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
    },
    ring: {
        position: 'absolute',
        width: 100,
        height: 100,
        borderRadius: 50,
        borderWidth: 2,
        borderColor: Colors.light.tint,
    },
    nameText: {
        fontWeight: 'bold',
        marginTop: -20,
    }
});