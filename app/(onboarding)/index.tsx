import { MAIN_STYLES } from "@/constants/Styles";
import { View, SafeAreaView, Text, Image, Pressable } from "react-native";
import { onboardingStyles } from "./_layout";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { ThemedText } from "@/components/ThemedText";
import { useRouter } from "expo-router";
import { getIsOnboardingCompleted } from "@/components/util/GeneralUtil";
import { useEffect } from "react";

export default function OnboardingCreateStories() {
    const router = useRouter();

    useEffect(() => {
        const checkOnboardingCompleted = async () => {
            const isOnboardingCompleted = await getIsOnboardingCompleted();
            if (isOnboardingCompleted) {
                router.replace('/(tabs)/(discover)');
            }
        };
        checkOnboardingCompleted();
    }, []);

    return <SafeAreaView style={MAIN_STYLES.safeArea}>
        <View style={onboardingStyles.mainView}>
            <View style={MAIN_STYLES.flex1} />
            <Image style={onboardingStyles.onboardingRoundImage} source={require('@/assets/images/onboarding/onboarding_create_stories.png')} resizeMode="cover" />
            <Text style={onboardingStyles.title}>{getTranslation('createAIStories')}</Text>
            <Text style={onboardingStyles.subTitle}>{getTranslation('createAIStoriesSubtitle')}</Text>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={onboardingStyles.onboardingButton} onPress={() => {
                router.push('/voices');
            }}>
                <Text style={MAIN_STYLES.mainButtonText}>{getTranslation('continue')}</Text>
            </Pressable>
        </View>
    </SafeAreaView>;
}