import { MAIN_STYLES } from "@/constants/Styles";
import { View, SafeAreaView, Text, Image, Pressable } from "react-native";
import { onboardingStyles } from "./_layout";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { ThemedText } from "@/components/ThemedText";
import { useRouter } from "expo-router";

export default function OnboardingVoices() {
    const router = useRouter();

    return <SafeAreaView style={MAIN_STYLES.safeArea}>
        <View style={onboardingStyles.mainView}>
            <View style={MAIN_STYLES.flex1} />
            <Image style={onboardingStyles.onboardingRoundImage} source={require('@/assets/images/onboarding/onboarding_voices.png')} resizeMode="cover" />
            <Text style={onboardingStyles.title}>{getTranslation('onboardingVoices')}</Text>
            <Text style={onboardingStyles.subTitle}>{getTranslation('onboardingVoicesSubtitle')}</Text>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={onboardingStyles.onboardingButton} onPress={() => {
                router.push('/clone1');
            }}>
                <Text style={MAIN_STYLES.mainButtonText}>{getTranslation('continue')}</Text>
            </Pressable>
        </View>
    </SafeAreaView>;
}