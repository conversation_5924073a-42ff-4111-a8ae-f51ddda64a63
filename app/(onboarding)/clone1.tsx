import { MAIN_STYLES } from "@/constants/Styles";
import { View, SafeAreaView, Text, Image, Pressable } from "react-native";
import { onboardingStyles } from "./_layout";
import { getTranslation } from "@/components/util/LocalizationUtil";
import { ThemedText } from "@/components/ThemedText";
import { useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { displayPaywall } from "@/components/util/AdaptyUtil";
import { setIsOnboardingCompleted } from "@/components/util/GeneralUtil";

export default function OnboardingClone1() {
    const router = useRouter();

    return <SafeAreaView style={MAIN_STYLES.safeArea}>
        <View style={onboardingStyles.mainView}>
            <View style={MAIN_STYLES.flex1} />
            <Image style={onboardingStyles.onboardingRoundImage} source={require('@/assets/images/onboarding/onboarding_clone.png')} resizeMode="cover" />
            <Text style={onboardingStyles.title}>{getTranslation('onboardingClone1')}</Text>
            <Text style={onboardingStyles.subTitle}>{getTranslation('onboardingClone1Subtitle')}</Text>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={onboardingStyles.onboardingButton} onPress={() => {
                router.push('/clone2');
            }}>
                <Text style={MAIN_STYLES.mainButtonText}>{getTranslation('cloneMyVoiceNow')}</Text>
            </Pressable>
            <Pressable style={[onboardingStyles.onboardingButton, { backgroundColor: Colors.light.blueCharcoal, marginTop: 8 }]} onPress={() => {
                router.push('/(tabs)/(discover)');
                // displayPaywall('general');
                setIsOnboardingCompleted();
            }}>
                <Text style={[MAIN_STYLES.mainButtonText]}>{getTranslation('skipThisStep')}</Text>
            </Pressable>
        </View>
    </SafeAreaView>;
}