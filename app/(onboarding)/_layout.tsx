import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { Stack } from "expo-router";
import { StyleSheet } from "react-native";

export default function OnboardingLayout() {
    return (
        <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="index" />
            <Stack.Screen name="voices" />
            <Stack.Screen name="clone1" />
            <Stack.Screen name="clone2" />
            <Stack.Screen name="clone3" />
            <Stack.Screen name="clone4" />
        </Stack>
    );
}

export const onboardingStyles = StyleSheet.create({
    onboardingRoundImage: {
        width: 240,
        height: 240,
        borderRadius: 120,
        overflow: 'hidden',
    },
    mainView: {
        ...MAIN_STYLES.mainView,
        alignItems: 'center',
        justifyContent: 'center',
    },
    title: {
        fontSize: 32,
        fontWeight: '400',
        color: Colors.light.text,
        marginTop: 24,
        marginBottom: 8,
        textAlign: 'center',
    },
    subTitle: {
        color: "#EBEBF599",
        fontSize: 16,
        fontWeight: '400',
        textAlign: 'center',
    },
    onboardingButton: {
        backgroundColor: Colors['light'].tint,
        height: 56,
        width: '100%',
        padding: 16,
        borderRadius: 28,
        alignItems: 'center',
        justifyContent: 'center',
    }
});