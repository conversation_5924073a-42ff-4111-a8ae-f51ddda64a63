import { ChildProfile } from "@/contexts/StoryContext";
import AsyncStorage from '@react-native-async-storage/async-storage';

const CHILD_PROFILE_KEY = 'CHILD_PROFILE';

export const saveChildProfile = async (childProfile: ChildProfile) => {
    await AsyncStorage.setItem(CHILD_PROFILE_KEY, JSON.stringify(childProfile));
};

export const getChildProfile = async (): Promise<ChildProfile | null> => {
    const childProfile = await AsyncStorage.getItem(CHILD_PROFILE_KEY);
    return childProfile ? JSON.parse(childProfile) : null;
};

export const clearChildProfile = async () => {
    await AsyncStorage.removeItem(CHILD_PROFILE_KEY);
};