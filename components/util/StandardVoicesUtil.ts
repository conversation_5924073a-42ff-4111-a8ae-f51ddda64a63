import { getLocale } from "./LocalizationUtil";

export const STANDARD_VOICES_PREVIEW_FILES = {
    'tr': {
        'male': require('@/assets/standardVoices/tr_male.mp3'),
        'female': require('@/assets/standardVoices/tr_female.mp3')
    },
    'en': {
        'male': require('@/assets/standardVoices/en_male.mp3'),
        'female': require('@/assets/standardVoices/en_female.mp3')
    }
}

export const getStandardVoicePreviewFile = (type: number) => {
    const locale = getLocale();
    return STANDARD_VOICES_PREVIEW_FILES[locale][type === 1 ? 'female' : 'male'];
}