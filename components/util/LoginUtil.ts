import 'react-native-get-random-values';
import { v7 as uuidv7 } from 'uuid';
import { API_URL } from '../../constants/Constants';
import AsyncStorage from '@react-native-async-storage/async-storage';

const USER_ID_KEY = 'USER_ID';

export const getUserId = async (): Promise<string | null> => {
	return await AsyncStorage.getItem(USER_ID_KEY);
};

export const getUserIdCreateIfNot = async (): Promise<string | null> => {
	try {
		// Prevent saving it again if it is already there
		const existingUserId = await AsyncStorage.getItem(USER_ID_KEY);
		if (existingUserId) {
			return existingUserId;
		}

		// Generate new UUID v7
		const newUserId = uuidv7();

		await AsyncStorage.setItem(USER_ID_KEY, newUserId);
		return newUserId;
	} catch (e) {
		console.error('Failed to save user id', e);
		return '';
	}
};

export const appLoginFlow = async () => {
	// TODO: Alert
	try {
		const userId = await getUserIdCreateIfNot();
		if (!userId) {
			throw new Error('User id not found');
		}
		console.log("User Id", userId)

		const response = await fetch(`${API_URL}/user_login`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				identifier: userId,
			}),
		});

		const json = await response.json();
		console.log('User login response: ', json);

		if (json.status !== 'OK') {
			throw new Error('User login failed json.status');
		}
	} catch (error) {
		console.error('User login failed', error);
	}
};