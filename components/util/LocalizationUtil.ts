import { getLocales } from 'expo-localization';
import en from '@/localizations/en.json';
import tr from '@/localizations/tr.json';
import { I18n } from 'i18n-js';

const translations = {
    en: en,
    tr: tr
}
const i18n = new I18n(translations);

// Set the locale once at the beginning of your app.
i18n.locale = getLocales()[0].languageCode ?? 'en';

// When a value is missing from a language it'll fall back to another language with the key present.
i18n.enableFallback = true;

export const getLocale = () => {
    const detectedLanguage = getLocales()[0].languageCode ?? 'en';
    return detectedLanguage in translations ? detectedLanguage : 'en';
}

export const getTranslation = (key: string) => {
    return i18n.t(key);
}

export const getTranslationWithParams = (key: string, params: any) => {
    return i18n.t(key, params);
}