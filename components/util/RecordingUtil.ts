import AsyncStorage from '@react-native-async-storage/async-storage';

const RECORDINGS_KEY = 'VOICE_RECORDINGS';

export interface RecordingItem {
	name: string;
	voiceId: number;
	isDeleted: boolean
}

export const loadRecordings = async (): Promise<RecordingItem[]> => {
	try {
		const savedRecordings = await AsyncStorage.getItem(RECORDINGS_KEY);
		if (savedRecordings !== null) {
			// console.log('Loaded recordings:', JSON.parse(savedRecordings));
			return JSON.parse(savedRecordings);
		} else {
			return [];
		}
	} catch (error) {
		console.error('Failed to load recordings', error);
		return [];
	}
};

export const saveRecordings = async (newRecording: RecordingItem) => {
	try {
		const recordings = await loadRecordings();
		const newRecordings = [...recordings, newRecording];
		await AsyncStorage.setItem(RECORDINGS_KEY, JSON.stringify(newRecordings));
	} catch (error) {
		console.error('Failed to save recordings', error);
	}
};

export const deleteRecording = async (voiceId: number) => {
	try {
		const recordings = await loadRecordings();
		const recordingToDelete = recordings.find((recording) => recording.voiceId === voiceId);
		if (recordingToDelete) {
			recordingToDelete.isDeleted = true;
			await AsyncStorage.setItem(RECORDINGS_KEY, JSON.stringify(recordings));
		}
	} catch (error) {
		console.error('Failed to delete recording', error);
	}
};