import AsyncStorage from '@react-native-async-storage/async-storage';

const STORIES_MAIN_KEY = 'STORIES_MAIN_';
const CURATED_STORIES_MAIN_KEY = 'CURATED_STORIES_MAIN_2_';
const STORY_KEY = 'STORY_';
const FAV_STORIES_KEY = 'FAV_STORIES';
const READINGS_KEY = 'READINGS_';

// INTERFACES
export interface StoryItem {
	id: number;
	title: string;
	createDate?: string;
	storyKey: string;
	storyText?: string | null;
	curated?: boolean;
	imageLastUpdateTime?: string;
	textLastUpdateTime?: string;
}

export interface RemoteStoryItem {
	id: number;
	text: string;
	image_last_update_time: string;
	text_last_update_time: string;
}

// INTERNAL FUNCTIONS
const saveStoryText = async (newStory: string, id: number) => {
	try {
		await AsyncStorage.setItem(STORY_KEY + id, newStory);
	} catch (error) {
		console.error('Failed to save story text', error);
	}
};

// EXTERNAL FUNCTIONS
export const saveStory = async (
	storyText: string,
	storyId: number,
) => {
	try {
		// Eliminate markdown characters
		storyText = storyText.replaceAll('**', '');
		// Extract title
		const title = storyText.split('\n')[0];
		const storyTextNoTitle = storyText.split('\n').slice(1).join('\n');
		// Save the story text
		await saveStoryText(storyTextNoTitle, storyId);

		// Create the story element
		const newStory = {
			id: storyId,
			title: title,
			createDate: new Date().toISOString(),
			storyKey: STORY_KEY + storyId
		};
		console.log('NEW STORY\n', newStory);

		// Push the new story to the main stories list
		const currentStories = await loadStories();
		currentStories.push(newStory);

		// Save the new stories list
		await AsyncStorage.setItem(
			STORIES_MAIN_KEY,
			JSON.stringify(currentStories),
		);
	} catch (error) {
		console.error('Failed to save story', error);
	}
};

export const loadStories = async (): Promise<StoryItem[]> => {
	try {
		const savedStories = await AsyncStorage.getItem(STORIES_MAIN_KEY);
		if (savedStories !== null) {
			const stories = JSON.parse(savedStories);
			for (let i = 0; i < stories.length; i++) {
				const storyKey = stories[i].storyKey;
				stories[i].storyText = await AsyncStorage.getItem(storyKey);
			}
			return stories;
		} else {
			return [];
		}
	} catch (error) {
		console.error('Failed to load stories', error);
		return [];
	}
};

export const getStoryById = async (
	storyId: number,
	curated = false,
): Promise<StoryItem | null> => {
	try {
		let savedStories;
		if (curated) {
			savedStories = await AsyncStorage.getItem(CURATED_STORIES_MAIN_KEY);
		} else {
			savedStories = await AsyncStorage.getItem(STORIES_MAIN_KEY);
		}
		if (savedStories !== null) {
			const stories: StoryItem[] = JSON.parse(savedStories);
			const story = stories.find(s => s.id === storyId);
			if (story) {
				const storyText = await AsyncStorage.getItem(story.storyKey);
				return {
					...story,
					storyText,
				};
			}
		}
		return null;
	} catch (error) {
		console.error('Failed to load story', error);
		return null;
	}
};

export const deleteStory = async (storyId: number) => {
	const stories = await loadStories();
	const newStories = stories.filter(s => s.id !== storyId);
	await AsyncStorage.setItem(STORIES_MAIN_KEY, JSON.stringify(newStories));
	await AsyncStorage.removeItem(STORY_KEY + storyId);
};

export const getStoryDuration = (storyText: string): number => {
	const words = storyText.split(' ').length;
	const minutes = words / 150;
	return Math.round(minutes * 2) / 2;
};

// CURATED STORIES
export const loadCuratedStories = async (): Promise<StoryItem[]> => {
	try {
		const savedStories = await AsyncStorage.getItem(CURATED_STORIES_MAIN_KEY);
		if (savedStories !== null) {
			const stories = JSON.parse(savedStories);
			for (let i = 0; i < stories.length; i++) {
				const storyKey = stories[i].storyKey;
				stories[i].storyText = await AsyncStorage.getItem(storyKey);
			}
			return stories;
		} else {
			return [];
		}
	} catch (error) {
		console.error('Failed to load curated stories', error);
		return [];
	}
};

export const saveCuratedStories = async (remoteStories: RemoteStoryItem[]) => {
	try {
		let currentStories = await loadCuratedStories();

		// Remove stories that are no longer in the remote list
		const remoteIds = remoteStories.map(s => s.id);
		currentStories = currentStories.filter(currentStory => remoteIds.includes(currentStory.id));
		const currentIds = currentStories.map(s => s.id);

		// Add new stories
		for (let i = 0; i < remoteStories.length; i++) {
			const remoteStory = remoteStories[i];

			if (!currentIds.includes(remoteStory.id)) {
				// Extract title
				const title = remoteStory.text.split('\n')[0];
				const storyTextNoTitle = remoteStory.text.split('\n').slice(1).join('\n');
				// Save the story text
				await saveStoryText(storyTextNoTitle, remoteStory.id);

				// Create the story element
				const newStory = {
					id: remoteStory.id,
					title: title,
					createDate: new Date().toISOString(),
					storyKey: STORY_KEY + remoteStory.id,
					curated: true,
					imageLastUpdateTime: remoteStory.image_last_update_time,
					textLastUpdateTime: remoteStory.text_last_update_time,
				};
				console.log('NEW STORY\n', newStory);

				// Push the new story to the main stories list
				currentStories.push(newStory);
			} else {
				// If it already exists, update the image & text update times
				const currentStory = currentStories.find(s => s.id === remoteStory.id);
				if (currentStory) {
					currentStory.imageLastUpdateTime = remoteStory.image_last_update_time;
					currentStory.textLastUpdateTime = remoteStory.text_last_update_time;
				}
			}

			// If exists but text is updated, set it as well
			if (currentIds.includes(remoteStory.id) && remoteStory.text) {
				// Update title:
				const title = remoteStory.text.split('\n')[0];
				console.log("Updating story title for story id: ", remoteStory.id, title);
				const currentStory = currentStories.find(s => s.id === remoteStory.id);
				if (currentStory) {
					currentStory.title = title;
				}
				// Save the story text
				const storyTextNoTitle = remoteStory.text.split('\n').slice(1).join('\n');
				console.log("Updating story text for story id: ", remoteStory.id);
				await saveStoryText(storyTextNoTitle, remoteStory.id);
			}
		}
		// Sort the stories by id (descending)
		currentStories.sort((a, b) => b.id - a.id);
		// Save the new stories list
		await AsyncStorage.setItem(
			CURATED_STORIES_MAIN_KEY,
			JSON.stringify(currentStories),
		);
	} catch (error) {
		console.error('Failed to save curated stories', error);
	}
}

// STORY FAVORITES
export const getFavStoryIds = async (): Promise<number[]> => {
	const savedFavs = await AsyncStorage.getItem(FAV_STORIES_KEY);
	if (savedFavs !== null) {
		return JSON.parse(savedFavs);
	}
	return [];
};

export const addFavStoryId = async (storyId: number) => {
	const favIds = await getFavStoryIds();
	favIds.push(storyId);
	await AsyncStorage.setItem(FAV_STORIES_KEY, JSON.stringify(favIds));
};

export const removeFavStoryId = async (storyId: number) => {
	const favIds = await getFavStoryIds();
	const newFavIds = favIds.filter(id => id !== storyId);
	await AsyncStorage.setItem(FAV_STORIES_KEY, JSON.stringify(newFavIds));
};

// STORY READINGS
export const getStoryReadings = async (storyId: number) => {
	const savedReadings = await AsyncStorage.getItem(READINGS_KEY + storyId);
	if (savedReadings !== null) {
		return JSON.parse(savedReadings);
	}
	return [];
};

export const saveStoryReadings = async (storyId: number, readings: any) => {
	await AsyncStorage.setItem(READINGS_KEY + storyId, JSON.stringify(readings));
};
