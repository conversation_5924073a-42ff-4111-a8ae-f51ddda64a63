export const hashTo32CharString = (inputString: string): string => {
	// Initialize array of prime numbers for better hash distribution
	const primes = [5381, 52711, 61859, 32533];
	const hashes = [...primes];

	// For each character, update all hash values
	for (const char of inputString) {
		const charCode = char.charCodeAt(0);
		for (let i = 0; i < hashes.length; i++) {
			hashes[i] = (hashes[i] * 33) ^ charCode;
		}
	}

	// Convert hashes to padded hex strings and concatenate
	return hashes
		.map(hash => (hash >>> 0).toString(16).padStart(8, '0'))
		.join('');
};