import AsyncStorage from "@react-native-async-storage/async-storage";
import { Alert } from "react-native";
import { getTranslation } from "./LocalizationUtil";
const IS_ONBOARDING_COMPLETED_KEY = 'IS_ONBOARDING_COMPLETED';
const IS_DEV_MODE_KEY = 'IS_DEV_MODE';

export const parseIntAtLeastTwoDigits = (inputNumber: number) => {
    return Math.max(2, inputNumber.toString().length);
}

export const getIsOnboardingCompleted = async () => {
    const isCompleted = await AsyncStorage.getItem(IS_ONBOARDING_COMPLETED_KEY);
    return isCompleted === '1';
}

export const setIsOnboardingCompleted = async () => {
    await AsyncStorage.setItem(IS_ONBOARDING_COMPLETED_KEY, '1');
}

export const resetIsOnboardingCompleted = async () => {
    await AsyncStorage.removeItem(IS_ONBOARDING_COMPLETED_KEY);
    Alert.alert(getTranslation('onboardingReset'));
}

export const getIsDevMode = async () => {
    const isDevMode = await AsyncStorage.getItem(IS_DEV_MODE_KEY);
    return isDevMode === '1';
}

export const setIsDevMode = async (isDevMode: boolean) => {
    await AsyncStorage.setItem(IS_DEV_MODE_KEY, isDevMode ? '1' : '0');
}

export const resetIsDevMode = async () => {
    await AsyncStorage.removeItem(IS_DEV_MODE_KEY);
}