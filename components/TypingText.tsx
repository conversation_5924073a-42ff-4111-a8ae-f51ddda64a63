import { useState, useEffect } from "react";
import { Text, StyleSheet } from "react-native";

const TypingText = ({ text }: { text: string }) => {
    const [displayedText, setDisplayedText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        setDisplayedText('');
        setCurrentIndex(0);
    }, [text]);

    useEffect(() => {
        if (currentIndex < text.length) {
            const timeout = setTimeout(() => {
                setDisplayedText(prev => prev + text[currentIndex]);
                setCurrentIndex(prev => prev + 1);
            }, 50); // Adjust typing speed here

            return () => clearTimeout(timeout);
        }
    }, [currentIndex, text]);

    return <Text style={styles.subText}>{displayedText}</Text>;
};

const styles = StyleSheet.create({
    subText: {
        fontSize: 16,
        fontWeight: '400',
        color: '#EBEBF599',
        textAlign: 'center',
    }
});

export default TypingText;