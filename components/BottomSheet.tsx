import { ReactNode, useCallback, useEffect } from 'react';
import { StyleSheet, View, Dimensions, Modal, Pressable } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  Extrapolate,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
  withTiming,
} from 'react-native-reanimated';
import { Colors } from '@/constants/Colors';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

type Props = {
  isVisible: boolean;
  onClose: () => void;
  children: ReactNode;
  height?: number;
};

export default function BottomSheet({ isVisible, onClose, children, height = SCREEN_HEIGHT * 0.6 }: Props) {
  const translateY = useSharedValue(height);
  const active = useSharedValue(false);

  const scrollTo = useCallback((destination: number) => {
    'worklet';
    active.value = destination !== height;
    translateY.value = withSpring(destination, {
      damping: 50,
      mass: 0.3,
    });
  }, [height]);

  const isClosing = useCallback((velocity: number) => {
    'worklet';
    const shouldClose = 
      (translateY.value > height * 0.6) || 
      (velocity > 20);
    return shouldClose;
  }, [height]);

  useEffect(() => {
    if (isVisible) {
      scrollTo(0);
    } else {
      scrollTo(height);
    }
  }, [isVisible, height, scrollTo]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      translateY.value = translateY.value;
    })
    .onUpdate((event) => {
      translateY.value = Math.max(0, Math.min(height, event.translationY));
    })
    .onEnd((event) => {
      if (isClosing(event.velocityY)) {
        runOnJS(onClose)();
      } else {
        scrollTo(0);
      }
    });

  const rBottomSheetStyle = useAnimatedStyle(() => {
    const borderRadius = interpolate(
      translateY.value,
      [0, height],
      [16, 16],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ translateY: translateY.value }],
      borderRadius,
    };
  });

  const rBackdropStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(active.value ? 1 : 0)
    };
  }, []);

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent
      statusBarTranslucent
      onRequestClose={onClose}
      animationType="none"
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, rBackdropStyle]}>
          <Pressable style={styles.backdropPressable} onPress={onClose} />
        </Animated.View>
        <GestureDetector gesture={gesture}>
          <Animated.View style={[styles.bottomSheetContainer, { height }, rBottomSheetStyle]}>
            <View style={styles.handle} />
            {children}
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  backdropPressable: {
    flex: 1,
  },
  bottomSheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    zIndex: 2,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: Colors['light'].text,
    alignSelf: 'center',
    borderRadius: 2,
    marginBottom: 8,
  },
}); 