import { Colors } from '@/constants/Colors';
import { StyleSheet, Animated } from 'react-native';
import { useRef, useEffect } from 'react';

interface CloneImagePlaceholderProps {
    name: string;
    voiceId: string;
}

export const CloneImagePlaceholder = ({ name, voiceId }: CloneImagePlaceholderProps) => {
    const animatedValue = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(animatedValue, {
                    toValue: 1,
                    duration: 2000,
                    useNativeDriver: false
                }),
                Animated.timing(animatedValue, {
                    toValue: 0,
                    duration: 2000,
                    useNativeDriver: false
                })
            ])
        ).start();
    }, []);

    const generateBaseColor = (name: string, voiceId: string) => {
        if (!name) {
            name = "undefined";
        }
        // Generate hash from name
        const nameHash = name.split('').reduce((acc, char) => {
            return char.charCodeAt(0) + ((acc << 5) - acc);
        }, 0);

        // Combine with voiceId using multiplication and large prime numbers
        const combinedHash = (nameHash * 31) * ((parseInt(voiceId) || 1) * 17);
        const hue = Math.abs(combinedHash % 360);
        return `hsl(${hue}, 70%, 60%)`;
    };

    const baseColor = generateBaseColor(name, voiceId);
    const lighterColor = baseColor.replace('60%', '80%');

    const interpolatedColor = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [baseColor, lighterColor]
    });

    return (
        <Animated.View 
            style={[
                styles.voiceSelectionImage,
                {
                    backgroundColor: interpolatedColor
                }
            ]} 
        />
    );
};

const styles = StyleSheet.create({
    voiceSelectionImage: {
        width: 48,
        height: 48,
        borderRadius: 8,
        marginRight: 8,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: Colors['light'].tint
    }
});
