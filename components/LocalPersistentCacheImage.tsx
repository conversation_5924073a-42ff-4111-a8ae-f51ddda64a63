import { useEffect, useState } from 'react';
import { Image } from 'react-native';
import { hashTo32CharString } from '@/components/util/HashUtil';
import * as FileSystem from 'expo-file-system';

const LocalPersistentCacheImage = ({
	url,
	style,
	defaultImageUrl = 'https://storyai-files.dofatech.com/images/default_story_image.webp'
}: {
	url: string,
	style: any,
	defaultImageUrl?: string
}) => {
	const [pathToShow, setPathToShow] = useState('');
	const [isLocal, setIsLocal] = useState(false);

	useEffect(() => {
		const checkAndDownloadImage = async () => {
			const getLocalPath = (imageUrl: string) => {
				const urlSplit = imageUrl.split('.');
				const extension = urlSplit[urlSplit.length - 1].split('?')[0];
				const uniqueId = hashTo32CharString(imageUrl);
				return `${FileSystem.documentDirectory}image_${uniqueId}.${extension}`;
			};

			const localPath = getLocalPath(url);
			const fileInfo = await FileSystem.getInfoAsync(localPath);

			if (fileInfo.exists) {
				setIsLocal(true);
				setPathToShow(localPath);
			} else {
				try {
					const remoteCheck = await fetch(url, { method: 'HEAD' });
					if (remoteCheck.ok) {
						setPathToShow(url);
						const downloadResult = await FileSystem.downloadAsync(url, localPath);
						console.log('File downloaded:', downloadResult);
					} else {
						console.warn('Remote image not found:', url);
						if (defaultImageUrl) {
							const defaultLocalPath = getLocalPath(defaultImageUrl);
							const defaultFileInfo = await FileSystem.getInfoAsync(defaultLocalPath);
							
							if (defaultFileInfo.exists) {
								setIsLocal(true);
								setPathToShow(defaultLocalPath);
							} else {
								setPathToShow(defaultImageUrl);
								const downloadResult = await FileSystem.downloadAsync(defaultImageUrl, defaultLocalPath);
								console.log('Default image downloaded:', downloadResult);
							}
						}
					}
				} catch (error) {
					console.error('Error checking/downloading file:', error);
					if (defaultImageUrl) {
						const defaultLocalPath = getLocalPath(defaultImageUrl);
						const defaultFileInfo = await FileSystem.getInfoAsync(defaultLocalPath);
						
						if (defaultFileInfo.exists) {
							setIsLocal(true);
							setPathToShow(defaultLocalPath);
						} else {
							setPathToShow(defaultImageUrl);
							try {
								const downloadResult = await FileSystem.downloadAsync(defaultImageUrl, defaultLocalPath);
								console.log('Default image downloaded:', downloadResult);
							} catch (defaultError) {
								console.error('Error downloading default image:', defaultError);
							}
						}
					}
				}
			}
		};

		checkAndDownloadImage();
	}, [url, defaultImageUrl]);

	return pathToShow === '' ? (
		<></>
	) : (
		<Image
			style={[
				style,
				// { borderColor: isLocal ? 'green' : 'red', borderWidth: 2 },
			]}
			source={{ uri: pathToShow }}
		/>
	);
};

export default LocalPersistentCacheImage;
