import { Text, TextInput, View } from "react-native";
import { Colors } from "@/constants/Colors";

const TextInputwLabel = ({ 
    label, 
    value, 
    onChangeText, 
    placeholder, 
    numeric = false
}: { 
    label: string, 
    value: string, 
    onChangeText: (text: string) => void, 
    placeholder: string,
    numeric?: boolean
}) => {
    return (
        <View style={styles.container}>
            <Text style={styles.label}>{label}</Text>
            <TextInput 
                style={styles.input}
                value={value} 
                onChangeText={onChangeText} 
                placeholder={placeholder}
                placeholderTextColor="#666"
                keyboardType={numeric ? "numeric" : "default"}
            />
        </View>
    )
}

const styles = {
    container: {
        backgroundColor: '#1c2333',
        paddingVertical: 16,
        paddingHorizontal: 16,
        borderRadius: 16,
        marginBottom: 16
    },
    label: {
        color: Colors.light.gray,
        marginBottom: 4,
        fontSize: 12,
        fontWeight: 'bold'
    },
    input: {
        color: '#fff',
        borderRadius: 8,
        fontSize: 16,
    }
}

export default TextInputwLabel;