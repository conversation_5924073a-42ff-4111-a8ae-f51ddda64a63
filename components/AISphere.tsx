import React, { useRef, useEffect } from 'react';
import { View, Animated, StyleSheet, Easing } from 'react-native';
import { Colors } from '@/constants/Colors';

export const AISphere = () => {
    const orbitAnim = useRef(new Animated.Value(0)).current;
    const ringAnim = useRef(new Animated.Value(0)).current;
    const glowAnim = useRef(new Animated.Value(1)).current;
    const colorAnim = useRef(new Animated.Value(0)).current;
    
    const getRandomParticleColor = () => {
        const colors = [
            '#00ffff',             // Cyan
            '#00c8ff',             // Light blue
            '#0096ff',             // Azure blue
            '#6600ff',             // Blue purple
            '#8000ff',             // Bright purple
            '#a000ff',             // Light purple
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    };

    const particleAnims = useRef(
        Array(12).fill(0).map(() => ({
            position: new Animated.Value(0),
            opacity: new Animated.Value(1),
            scale: new Animated.Value(1),
            glow: new Animated.Value(1),
            color: getRandomParticleColor()
        }))
    ).current;

    useEffect(() => {
        // Core sphere color and glow animation
        Animated.loop(
            Animated.parallel([
                Animated.sequence([
                    Animated.timing(glowAnim, {
                        toValue: 1.2,
                        duration: 2000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: false,
                    }),
                    Animated.timing(glowAnim, {
                        toValue: 1,
                        duration: 2000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: false,
                    })
                ]),
                Animated.loop(
                    Animated.timing(colorAnim, {
                        toValue: 1,
                        duration: 6000,
                        easing: Easing.linear,
                        useNativeDriver: false,
                    }), 
                    { iterations: -1 }
                )
            ])
        ).start();

        // Rings rotation
        Animated.loop(
            Animated.timing(ringAnim, {
                toValue: 1,
                duration: 8000,
                easing: Easing.linear,
                useNativeDriver: false,
            })
        ).start();

        // Updated particle animations
        const animateParticle = (index: number) => {
            const anim = particleAnims[index];
            
            Animated.loop(
                Animated.sequence([
                    Animated.timing(anim.glow, {
                        toValue: 0.4,
                        duration: 1000 + Math.random() * 1000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: false,
                    }),
                    Animated.timing(anim.glow, {
                        toValue: 1,
                        duration: 1000 + Math.random() * 1000,
                        easing: Easing.inOut(Easing.ease),
                        useNativeDriver: false,
                    })
                ])
            ).start();

            Animated.sequence([
                Animated.parallel([
                    Animated.timing(anim.position, {
                        toValue: 1,
                        duration: 1500 + Math.random() * 1000,
                        easing: Easing.out(Easing.cubic),
                        useNativeDriver: false,
                    }),
                    Animated.timing(anim.opacity, {
                        toValue: 0,
                        duration: 1500 + Math.random() * 1000,
                        easing: Easing.linear,
                        useNativeDriver: false,
                    }),
                    Animated.timing(anim.scale, {
                        toValue: 0.3,
                        duration: 1500 + Math.random() * 1000,
                        easing: Easing.linear,
                        useNativeDriver: false,
                    })
                ]),
                Animated.parallel([
                    Animated.timing(anim.position, {
                        toValue: 0,
                        duration: 0,
                        useNativeDriver: false,
                    }),
                    Animated.timing(anim.opacity, {
                        toValue: 1,
                        duration: 0,
                        useNativeDriver: false,
                    }),
                    Animated.timing(anim.scale, {
                        toValue: 1,
                        duration: 0,
                        useNativeDriver: false,
                    })
                ])
            ]).start(() => {
                setTimeout(() => animateParticle(index), Math.random() * 3000);
            });
        };

        // Start particle animations
        particleAnims.forEach((_, index) => {
            setTimeout(() => animateParticle(index), Math.random() * 2000);
        });
    }, []);

    const getRingColor = (ringIndex: number) => {
        const colors = [
            '#00ffff' + '90',      // Cyan
            '#00c8ff' + '90',      // Light blue
            '#0096ff' + '90',      // Azure blue
            '#6600ff' + '90',      // Blue purple
            '#8000ff' + '90',      // Bright purple
            '#a000ff' + '90',      // Light purple
        ];
        return colors[ringIndex];
    };

    const renderRings = () => {
        return [0, 1, 2, 3, 4, 5].map((ring) => (
            <Animated.View
                key={`ring-${ring}`}
                style={[
                    styles.ring,
                    {
                        borderColor: getRingColor(ring),
                        transform: [
                            { scale: 1.1 + ring * 0.1 },
                            {
                                rotateX: ringAnim.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: ['0deg', '360deg']
                                })
                            },
                            {
                                rotateY: ringAnim.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [`${ring * 30}deg`, `${ring * 30 + 360}deg`]
                                })
                            }
                        ]
                    }
                ]}
            />
        ));
    };

    const renderParticles = () => {
        return particleAnims.map((anim, index) => {
            const angle = (index * (360 / particleAnims.length)) * (Math.PI / 180);
            const radius = 60;

            return (
                <Animated.View
                    key={`particle-${index}`}
                    style={[
                        styles.particle,
                        {
                            backgroundColor: anim.color,
                            transform: [
                                {
                                    translateX: anim.position.interpolate({
                                        inputRange: [0, 1],
                                        outputRange: [Math.cos(angle) * radius, Math.cos(angle) * radius * 2]
                                    })
                                },
                                {
                                    translateY: anim.position.interpolate({
                                        inputRange: [0, 1],
                                        outputRange: [Math.sin(angle) * radius, Math.sin(angle) * radius * 2]
                                    })
                                },
                                { scale: anim.scale }
                            ],
                            opacity: Animated.multiply(anim.opacity, anim.glow),
                            shadowColor: anim.color,
                        }
                    ]}
                />
            );
        });
    };

    return (
        <View style={styles.sphereContainer}>
            <View style={[
                styles.glow,
                {
                    shadowColor: '#00ffff',  // We'll keep a static shadow color
                }
            ]}>
                <Animated.View 
                    style={[
                        styles.glowInner, 
                        { 
                            transform: [{ scale: glowAnim }],
                            backgroundColor: colorAnim.interpolate({
                                inputRange: [0, 0.33, 0.66, 1],
                                outputRange: [
                                    '#00ffff30',  // Cyan
                                    '#00c8ff30',  // Light blue
                                    '#0096ff30',  // Azure blue
                                    '#00ffff30',  // Back to cyan
                                ]
                            }),
                        }
                    ]} 
                />
            </View>
            <View style={[
                styles.innerSphereContainer,
                {
                    shadowColor: '#00ffff',  // We'll keep a static shadow color
                }
            ]}>
                <Animated.View 
                    style={[
                        styles.innerSphere,
                        {
                            backgroundColor: colorAnim.interpolate({
                                inputRange: [0, 0.33, 0.66, 1],
                                outputRange: [
                                    '#00ffff',  // Cyan
                                    '#00c8ff',  // Light blue
                                    '#0096ff',  // Azure blue
                                    '#00ffff',  // Back to cyan
                                ]
                            }),
                        }
                    ]} 
                />
            </View>
            {renderRings()}
            {renderParticles()}
        </View>
    );
};

const styles = StyleSheet.create({
    sphereContainer: {
        width: 240,
        height: 240,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 16,
    },
    glow: {
        position: 'absolute',
        width: 80,
        height: 80,
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.7,
        shadowRadius: 25,
    },
    glowInner: {
        width: '100%',
        height: '100%',
        borderRadius: 40,
    },
    innerSphereContainer: {
        width: 60,
        height: 60,
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.9,
        shadowRadius: 15,
    },
    innerSphere: {
        width: '100%',
        height: '100%',
        borderRadius: 30,
    },
    ring: {
        position: 'absolute',
        width: 90,
        height: 90,
        borderRadius: 45,
        borderWidth: 2,  // Slightly thicker border
        shadowColor: '#00ffff',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.7,
        shadowRadius: 8,
    },
    particle: {
        position: 'absolute',
        width: 6,
        height: 6,
        borderRadius: 3,
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 1,  // Full opacity for stronger glow
        shadowRadius: 6,   // Larger radius for more spread
    },
}); 