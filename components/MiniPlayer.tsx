import { View, Text, TouchableOpacity, StyleSheet, Pressable, Image } from 'react-native';
import { useAudio } from '@/contexts/AudioContext';
import { Colors } from '@/constants/Colors';
import Ionicons from '@expo/vector-icons/Ionicons';
import { router } from 'expo-router';
import { ViewStyle } from 'react-native';
import { MINI_PLAYER_HEIGHT, S3_URL_STORY_IMAGES } from '@/constants/Constants';
import LocalPersistentCacheImage from '@/components/LocalPersistentCacheImage';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

export default function MiniPlayer({ style }: { style: ViewStyle }) {
  const { currentSound, isPlaying, pauseSound, resumeSound, seekTo, stopSound } = useAudio();

  if (!currentSound) return null;

  const handlePlayPause = async () => {
    if (isPlaying) {
      await pauseSound();
    } else {
      await resumeSound();
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSeek = async (offset: number) => {
    if (currentSound) {
      await seekTo(currentSound.position + offset);
    }
  };

  return (
    <Pressable
      style={[styles.container, style]}
      onPress={() => router.push('/player')}>
      {currentSound.storyId &&
        <LocalPersistentCacheImage
          url={`${S3_URL_STORY_IMAGES}/image_${currentSound.storyId}.webp`}
          style={styles.image}
        />
      }
      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={1}>
          {currentSound.title}
        </Text>
        <Text style={styles.subtitle}>
          {formatTime(currentSound.position)} / {formatTime(currentSound.duration)}
        </Text>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={(e) => {
            e.stopPropagation();
            handleSeek(-10);
          }}>
          <MaterialCommunityIcons name="rewind-10" size={20} color={Colors['light'].text} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={(e) => {
            e.stopPropagation();
            handleSeek(10);
          }}>
          <MaterialCommunityIcons name="fast-forward-10" size={20} color={Colors['light'].text} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.playButton}
          onPress={(e) => {
            e.stopPropagation();
            handlePlayPause();
          }}>
          <Ionicons
            name={isPlaying ? "pause" : "play"}
            size={24}
            color={Colors['light'].text}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={(e) => {
            e.stopPropagation();
            stopSound();
          }}>
          <Ionicons name="close" size={20} color={Colors['light'].text} />
        </TouchableOpacity>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    height: MINI_PLAYER_HEIGHT,
    backgroundColor: Colors['light'].tabBarBackground + 'EE',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: '#2D344B',
    marginBottom: -1
  },
  content: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    color: Colors['light'].text,
    fontSize: 16,
    fontWeight: '500',
  },
  subtitle: {
    color: '#9A9DAC',
    fontSize: 12,
    marginTop: 2,
  },
  playButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors['light'].tabIconSelected,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 4,
    marginRight: 12,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  controlButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors['light'].tabIconSelected,
    justifyContent: 'center',
    alignItems: 'center',
  },
});