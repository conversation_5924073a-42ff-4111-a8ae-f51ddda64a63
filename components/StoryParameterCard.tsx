import { Colors } from '@/constants/Colors';
import { View, Image, Text, StyleSheet, ImageSourcePropType } from 'react-native';

interface StoryParameterCardProps {
  width: number;
  image: ImageSourcePropType;
  text: string;
  isSelected: boolean;
}

const StoryParameterCard: React.FC<StoryParameterCardProps> = ({
  width,
  image,
  text,
  isSelected,
}) => {
  return (
    <View
      style={[
        styles.container,
        {
          width: width,
          height: width, // Make it square
          borderWidth: isSelected ? 3 : 0,
          borderColor: Colors.light.tint,
        },
      ]}
    >
      <Image
        source={image}
        style={styles.image}
        resizeMode="cover"
      />
      <View style={styles.textContainer}>
        <Text style={styles.text}>{text}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    borderRadius: 16,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)', // Black overlay with opacity
    alignItems: 'center',
  },
  text: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
});

export default StoryParameterCard;
