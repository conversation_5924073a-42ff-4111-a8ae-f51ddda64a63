import { useEffect, useState } from "react";
import { ActivityIndicator, Pressable, View, Text } from "react-native";
import { getTranslation } from "../util/LocalizationUtil";
import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { Ionicons } from "@expo/vector-icons";
import { VoiceRowStyles } from './VoiceRowStyles';
import { CloneImagePlaceholder } from '../commonComponents/readStory/CloneImagePlaceholder';
import { getClonePreviewUrl } from "@/constants/Constants";
import { useAudioPlayer } from 'expo-audio';

interface CloneRowProps {
    clone: any;
    showConfirmationModal: (action: {
        type: 'standard' | 'clone' | 'special';
        voiceId?: number;
        voiceType?: number;
    }) => void;
}

const CloneRow = ({ clone, showConfirmationModal }: CloneRowProps) => {
    const [isLoading, setIsLoading] = useState(false);
    const [isPlaying, setIsPlaying] = useState(false);
    const audioFile = { uri: getClonePreviewUrl(clone.voiceId) };
    const player = useAudioPlayer(audioFile);

    useEffect(() => {
        const handlePlaybackStatusUpdate = () => {
            if (player.playing !== isPlaying) {
                setIsPlaying(player.playing);
            }
        };

        // Check playback status periodically
        const interval = setInterval(handlePlaybackStatusUpdate, 100);

        return () => {
            clearInterval(interval);
            // TODO: Redo with new player lib
            /*
            if (player.playing) {
                player.pause();
            }
            */
        };
    }, [player.playing, isPlaying]);

    const playSound = async () => {
        try {
            if (isPlaying) return;

            setIsPlaying(true);
            player.play();
        } catch (error) {
            console.error('Error playing sound:', error);
            setIsPlaying(false);
        }
    };

    const handleCreateClone = () => {
        showConfirmationModal({
            type: 'clone',
            voiceId: clone.voiceId
        });
    }

    return (
        <View style={VoiceRowStyles.voiceSelectionRow}>
            <CloneImagePlaceholder name={clone.name} voiceId={clone.voiceId} />
            <Pressable
                style={VoiceRowStyles.voicePreviewButton}
                onPress={playSound}
            >
                {isPlaying ?
                    <ActivityIndicator size="small" color={Colors['light'].text} /> :
                    <Ionicons name="play" size={16} color={Colors['light'].text} />
                }
            </Pressable>
            <View style={MAIN_STYLES.col}>
                <Text style={VoiceRowStyles.voiceNameText}>{clone.name}</Text>
                <Text style={VoiceRowStyles.creditText}>{getTranslation('tenCredits')}</Text>
            </View>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={VoiceRowStyles.createReadingButton} onPress={handleCreateClone}>
                {isLoading ? <ActivityIndicator size={17} color={Colors['light'].text} /> :
                    <Text style={VoiceRowStyles.createCloneButtonText}>{getTranslation('createReading')}</Text>
                }
            </Pressable>
        </View>
    )
}

export default CloneRow;
