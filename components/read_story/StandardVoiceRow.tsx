import { useEffect, useState } from "react";
import { ActivityIndicator, Pressable, View, Text, Image, Alert } from "react-native";
import { getTranslation } from "../util/LocalizationUtil";
import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { Ionicons } from "@expo/vector-icons";
import { getStandardVoicePreviewFile } from "@/components/util/StandardVoicesUtil";
import { useAudioPlayer, useAudioPlayerStatus } from 'expo-audio';
import { VoiceRowStyles } from './VoiceRowStyles';

interface StandardVoiceRowProps {
    type: number;
    showConfirmationModal: (action: {
        type: 'standard' | 'clone' | 'special';
        voiceId?: number;
        voiceType?: number;
    }) => void;
}

const StandardVoiceRow = ({ type, showConfirmationModal }: StandardVoiceRowProps) => {
    const audioFile = getStandardVoicePreviewFile(type);
    const localPlayer = useAudioPlayer(audioFile);
    const localPlayerStatus = useAudioPlayerStatus(localPlayer);

    const [isPlaying, setIsPlaying] = useState(false);

    useEffect(() => {
        if (localPlayerStatus?.didJustFinish) {
            setIsPlaying(false);
        }
    }, [localPlayerStatus]);

    const playSound = async () => {
        try {
            if (localPlayer.playing) return;
            setIsPlaying(true);
            localPlayer.seekTo(0);
            localPlayer.play();
        } catch (error) {
            console.error('Error playing sound:', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            Alert.alert(getTranslation('error'), getTranslation('errorPlayingSound') + "\n" + errorMessage);
        }
    };

    const handleCreateReading = () => {
        showConfirmationModal({
            type: 'standard',
            voiceType: type
        });
    }

    return (
        <View style={VoiceRowStyles.voiceSelectionRow}>
            <Image source={type === 1 ? require("@/assets/images/standard_voice/standard_voice_female.jpg")
                : require("@/assets/images/standard_voice/standard_voice_male.jpg")}
                style={VoiceRowStyles.voiceSelectionImage} />
            <Pressable
                style={VoiceRowStyles.voicePreviewButton}
                onPress={playSound}
            >
                {isPlaying ?
                    <ActivityIndicator size="small" color={Colors['light'].text} /> :
                    <Ionicons name="play" size={16} color={Colors['light'].text} />
                }
            </Pressable>
            <View style={MAIN_STYLES.col}>
                <Text style={VoiceRowStyles.voiceNameText}>{type === 1 ? getTranslation('female') : getTranslation('male')}</Text>
                <Text style={VoiceRowStyles.creditText}>{getTranslation('oneCredit')}</Text>
            </View>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={VoiceRowStyles.createReadingButton} onPress={handleCreateReading}>
                <Text style={VoiceRowStyles.createCloneButtonText}>{getTranslation('createReading')}</Text>
            </Pressable>
        </View>
    )
}

export default StandardVoiceRow;