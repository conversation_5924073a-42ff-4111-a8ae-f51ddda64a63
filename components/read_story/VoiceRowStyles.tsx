import { StyleSheet } from 'react-native';
import { Colors } from '@/constants/Colors';

export const VoiceRowStyles = StyleSheet.create({
    voiceSelectionRow: {
        flexDirection: 'row',
        marginBottom: 8,
        borderBottomColor: '#2D344B',
        borderBottomWidth: 1,
        alignItems: 'center',
        paddingBottom: 8
    },
    voiceSelectionImage: {
        width: 48,
        height: 48,
        borderRadius: 8,
        marginRight: 8,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: Colors['light'].tint
    },
    voicePreviewButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: Colors.light.tint,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8
    },
    voiceNameText: {
        color: Colors['light'].text,
        fontWeight: '500',
        fontSize: 18
    },
    creditText: {
        color: Colors['light'].text,
        fontWeight: '400',
        fontSize: 12
    },
    createReadingButton: {
        backgroundColor: Colors.light.tint,
        borderRadius: 8,
        paddingVertical: 8,
        alignItems: 'center',
        paddingHorizontal: 8
    },
    createCloneButtonText: {
        color: Colors['light'].text,
        fontWeight: '500',
        fontSize: 13
    }
});