{"expo": {"name": "TaleAI", "slug": "storyai", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/taleai_logo_1024_3.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "platforms": ["ios", "android"], "githubUrl": "https://github.com/DoguD/TaleAI", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.dofatech.storyai", "infoPlist": {"UIBackgroundModes": ["audio"]}, "config": {"usesNonExemptEncryption": false}, "googleServicesFile": "./buildRelated/GoogleService-Info.plist"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/taleai_logo_1024_3.png", "backgroundColor": "#000000"}, "package": "com.dofatech.storyai", "googleServicesFile": "./buildRelated/google-services.json"}, "plugins": ["expo-router", "expo-secure-store", "expo-localization", "@react-native-firebase/app", "@react-native-firebase/crashlytics", "expo-tracking-transparency", ["expo-build-properties", {"ios": {"useFrameworks": "static", "deploymentTarget": "15.1"}, "android": {"minSdkVersion": 26}}], ["react-native-fbsdk-next", {"appID": "2071526976583852", "clientToken": "6c62e6809436a10c5ac9f42fd777e54b", "displayName": "TaleAI", "scheme": "fb2071526976583852", "advertiserIDCollectionEnabled": true, "autoLogAppEventsEnabled": true, "isAutoInitEnabled": true, "iosUserTrackingPermission": "This identifier will be used to deliver personalized ads to you."}], ["expo-font", {"fonts": ["./assets/fonts/Quicksand-VariableFont_wght.ttf", "./assets/fonts/OpenSans-Italic-VariableFont_wdth,wght.ttf", "./assets/fonts/OpenSans-VariableFont_wdth,wght.ttf"]}], ["expo-audio", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "8ea2332f-4f6c-41d6-9eda-4de5dc0c1b45"}}, "owner": "dogudugur-org"}}